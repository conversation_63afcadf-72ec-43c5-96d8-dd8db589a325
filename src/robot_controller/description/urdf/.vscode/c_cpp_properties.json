{"configurations": [{"browse": {"databaseFilename": "${default}", "limitSymbolsToIncludedHeaders": false}, "includePath": ["/home/<USER>/arm_teleop_ws/devel/include/**", "/opt/ros/noetic/include/**", "/usr/local/include/**", "/home/<USER>/arm_teleop_ws/src/qj2_dexglove/src/hand_link/include/**", "/home/<USER>/arm_teleop_ws/src/qj2_dexglove/src/hand_link_right/include/**", "/home/<USER>/arm_teleop_ws/src/qj2_dexglove/src/imu_interface/include/**", "/home/<USER>/arm_teleop_ws/src/qj2_dexglove/src/inspire_hand/include/**", "/home/<USER>/arm_teleop_ws/src/qj2_dexglove/src/inspire_hand_right/include/**", "/home/<USER>/arm_teleop_ws/src/robot_controller/include/**", "/home/<USER>/arm_teleop_ws/src/qj2_pose_detec/src/pose_mediapipe/include/**", "/home/<USER>/arm_teleop_ws/src/qj2_dexglove/src/hardware_interface/senseglove_hardware/include/**", "/home/<USER>/arm_teleop_ws/src/qj2_dexglove/src/hardware_interface/senseglove_hardware_builder/include/**", "/home/<USER>/arm_teleop_ws/src/qj2_dexglove/src/hardware_interface/senseglove_hardware_interface/include/**", "/usr/include/**"], "name": "ROS", "intelliSenseMode": "gcc-x64", "compilerPath": "/usr/bin/gcc", "cStandard": "gnu11", "cppStandard": "c++14"}], "version": 4}