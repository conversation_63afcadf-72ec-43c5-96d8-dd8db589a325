<robot xmlns:xacro="http://www.ros.org/wiki/xacro" name="arms_gen2">

    <xacro:property name="PI" value="3.141592654"/>

    <xacro:property name="base_mass" value="9.4962" />
    <xacro:property name="link1_mass" value="2.0769" />
    <xacro:property name="link2_mass" value="0.0535693858885412" />
    <xacro:property name="link3_mass" value="1.7543" />
    <xacro:property name="link4_mass" value="1.8511" />
    <xacro:property name="link5_mass" value="0.1362" />
    <xacro:property name="link6_mass" value="0.17352" />
    <xacro:property name="link7_mass" value="0.2298" />

    <xacro:property name="base_ori_xyz" value="0.0022699 -0.06556 0.00064895" />
    <xacro:property name="link1_r_ori_xyz" value="0.0054077 -1.0079E-06 0.049262" />
    <xacro:property name="link1_l_ori_xyz" value="0.0054077 1.0079E-06 -0.049262" />
    <xacro:property name="link2_r_ori_xyz" value="0.030445498299944 1.81823605061027E-08 -0.00992570305958366" />
    <xacro:property name="link2_l_ori_xyz" value="0.030445 -1.8182E-08 0.015926" />
    <xacro:property name="link3_r_ori_xyz" value="-0.00077096 3.2231E-05 0.040289" />
    <xacro:property name="link3_l_ori_xyz" value="-0.00077096 -3.2231E-05 -0.040289" />
    <xacro:property name="link4_r_ori_xyz" value="-0.00040895 -0.010852 -0.034138" />
    <xacro:property name="link4_l_ori_xyz" value="-0.00034786 -0.010852 0.03414" />
    <xacro:property name="link5_r_ori_xyz" value="7.1086E-05 0.00019673 0.033224" />
    <xacro:property name="link5_l_ori_xyz" value="-7.1086409115571E-05 0.000196728471945962 -0.0332241836787868" />
    <xacro:property name="link6_r_ori_xyz" value="0.00015397 -0.057524 -0.025482" />
    <xacro:property name="link6_l_ori_xyz" value="0.00015488 -0.057524 0.025486" />
    <xacro:property name="link7_r_ori_xyz" value="0.098419 0.0043052 -0.022831" />
    <xacro:property name="link7_l_ori_xyz" value="0.094481 0.0038405 0.021825" />

    <xacro:property name="base_ixx" value="0.099659" />
    <xacro:property name="base_ixy" value="-0.00034775" />
    <xacro:property name="base_ixz" value="-6.3845E-05" />
    <xacro:property name="base_iyy" value="0.05583" />
    <xacro:property name="base_iyz" value="0.00016509" />
    <xacro:property name="base_izz" value="0.060576" />
    <xacro:property name="link1_ixx" value="0.0014285" />
    <xacro:property name="link1_ixy" value="-1.2629E-06" />
    <xacro:property name="link1_ixz" value="1.0683E-05" />
    <xacro:property name="link1_iyy" value="0.0013313" />
    <xacro:property name="link1_iyz" value="5.6547E-07" />
    <xacro:property name="link1_izz" value="0.0012659" />
    <xacro:property name="link2_ixx" value="4.1138936208078E-05" />
    <xacro:property name="link2_ixy" value="4.90996195884821E-11" />
    <xacro:property name="link2_ixz" value="-1.55301891344656E-05" />
    <xacro:property name="link2_iyy" value="5.88910959145939E-05" />
    <xacro:property name="link2_iyz" value="-9.20100747991942E-12" />
    <xacro:property name="link2_izz" value="5.08847911638598E-05" />
    <xacro:property name="link3_ixx" value="0.00098646" />
    <xacro:property name="link3_ixy" value="1.4682E-06" />
    <xacro:property name="link3_ixz" value="-1.5794E-05" />
    <xacro:property name="link3_iyy" value="0.00099837" />
    <xacro:property name="link3_iyz" value="1.9423E-06" />
    <xacro:property name="link3_izz" value="0.0010774" />
    <xacro:property name="link4_ixx" value="0.0010294" />
    <xacro:property name="link4_ixy" value="1.7019E-06" />
    <xacro:property name="link4_ixz" value="1.8412E-06" />
    <xacro:property name="link4_iyy" value="0.00095158" />
    <xacro:property name="link4_iyz" value="-9.9823E-06" />
    <xacro:property name="link4_izz" value="0.0011633" />
    <xacro:property name="link5_ixx" value="6.0336E-05" />
    <xacro:property name="link5_ixy" value="3.3879E-07" />
    <xacro:property name="link5_ixz" value="-5.2411E-08" />
    <xacro:property name="link5_iyy" value="6.6225E-05" />
    <xacro:property name="link5_iyz" value="-1.2548E-07" />
    <xacro:property name="link5_izz" value="5.5827E-05" />
    <xacro:property name="link6_ixx" value="8.2852E-05" />
    <xacro:property name="link6_ixy" value="-1.5144E-07" />
    <xacro:property name="link6_ixz" value="-3.2863E-07" />
    <xacro:property name="link6_iyy" value="7.3636E-05" />
    <xacro:property name="link6_iyz" value="4.385E-06" />
    <xacro:property name="link6_izz" value="7.9464E-05" />
    <xacro:property name="link7_ixx" value="7.9303E-05" />
    <xacro:property name="link7_ixy" value="-1.3706E-06" />
    <xacro:property name="link7_ixz" value="-4.3132E-06" />
    <xacro:property name="link7_iyy" value="0.00011941" />
    <xacro:property name="link7_iyz" value="-7.6741E-07" />
    <xacro:property name="link7_izz" value="9.1235E-05" />
    

    <xacro:property name="joint1_r_ori_xyz" value="0 0 0.2016" />
    <xacro:property name="joint2_r_ori_xyz" value="0.0356 0 0.052" />
    <xacro:property name="joint3_r_ori_xyz" value="0.049 0 -0.0356" />
    <xacro:property name="joint4_r_ori_xyz" value="-0.041 0 0.1996" />
    <xacro:property name="joint5_r_ori_xyz" value="0 -0.1395 -0.041" />
    <xacro:property name="joint6_r_ori_xyz" value="0 0.0295 0.035" />
    <xacro:property name="joint7_r_ori_xyz" value="0.0295 -0.07 -0.0295" />
    <xacro:property name="joint1_l_ori_xyz" value="0 0 -0.2016" />
    <xacro:property name="joint2_l_ori_xyz" value="0.0356 0 -0.052" />
    <xacro:property name="joint3_l_ori_xyz" value="0.049 0 0.0356" />
    <xacro:property name="joint4_l_ori_xyz" value="-0.041 0 -0.1996" />
    <xacro:property name="joint5_l_ori_xyz" value="0 -0.1395 0.041" />
    <xacro:property name="joint6_l_ori_xyz" value="0 0.0295 -0.035" />
    <xacro:property name="joint7_l_ori_xyz" value="0.0295 -0.07 0.0295" />

    <xacro:property name="joint1_r_ori_rpy" value="0 0 0" />
    <xacro:property name="joint2_r_ori_rpy" value="${-PI/2} 0 ${-PI/2}" />
    <xacro:property name="joint3_r_ori_rpy" value="${PI/2} 0 ${PI/2}" />
    <xacro:property name="joint4_r_ori_rpy" value="${-PI/2} 0 ${PI/2}" />
    <xacro:property name="joint5_r_ori_rpy" value="${PI/2} 0 0" />
    <xacro:property name="joint6_r_ori_rpy" value="${-PI/2} 0 0" />
    <xacro:property name="joint7_r_ori_rpy" value="${-PI/2} 0 ${-PI/2}" />
    <xacro:property name="joint1_l_ori_rpy" value="0 0 0" />
    <xacro:property name="joint2_l_ori_rpy" value="${PI/2} 0 ${-PI/2}" />
    <xacro:property name="joint3_l_ori_rpy" value="${-PI/2} 0 ${PI/2}" />
    <xacro:property name="joint4_l_ori_rpy" value="${PI/2} 0 ${PI/2}" />
    <xacro:property name="joint5_l_ori_rpy" value="${-PI/2} 0 0" />
    <xacro:property name="joint6_l_ori_rpy" value="${PI/2} 0 0" />
    <xacro:property name="joint7_l_ori_rpy" value="${PI/2} 0 ${-PI/2}" />

    <xacro:property name="base_rgba" value="0.75294 0.75294 0.75294 1" />
    <xacro:property name="link_rgba" value="0.89804 0.91765 0.92941 1" />

    <xacro:macro name="add_link" params="link_name ori_xyz mass ixx ixy ixz iyy iyz izz flag rgba">
        <link name="${link_name}">
            <inertial>
                <origin xyz="${ori_xyz}" rpy="0 0 0" />
                <mass value="${mass}" />
                <!-- <inertia ixx="${ixx}" ixy="${flag*ixy}" ixz="${flag*ixz}" iyy="${iyy}" iyz="${iyz}" izz="${izz}" /> -->
                <!-- <mass value="0" /> -->
                <inertia ixx="0" ixy="0" ixz="0" iyy="0" iyz="0" izz="0" />
            </inertial>
            <visual>
                <origin xyz="0 0 0" rpy="0 0 0" />
                <geometry>
                    <mesh filename="package://robot_controller/description/meshes_arms_gen2/${link_name}.STL" />
                </geometry>
                <material name="${link_name}_color">
                    <color rgba="${rgba}" />
                </material>
            </visual>
            <collision>
                <origin xyz="0 0 0" rpy="0 0 0" />
                <geometry>
                    <mesh filename="package://robot_controller/description/meshes_arms_gen2/${link_name}.STL" />
                </geometry>
            </collision>
        </link>
    </xacro:macro>

    <xacro:macro name="add_joint" params="joint_name parent_link child_link origin_xyz origin_rpy">
        <joint name="${joint_name}" type="revolute">
            <origin xyz="${origin_xyz}" rpy="${origin_rpy}" />
            <parent link="${parent_link}" />
            <child link="${child_link}" />
            <axis xyz="0 0 1" />
            <limit lower="-3.14" upper="3.14" effort="100" velocity="1" />
            <dynamics damping="100" friction="0" />
        </joint>
    </xacro:macro>

    <xacro:macro name="add_trans" params="joint_name">
        <transmission name="${joint_name}_trans">
            <type>transmission_interface/SimpleTransmission</type>
            <actuator name="${joint_name}_motor">
                <hardwareInterface>hardware_interface/EffortJointInterface</hardwareInterface>
                <mechanicalReduction>1</mechanicalReduction>
            </actuator>
            <joint name="${joint_name}">
                <hardwareInterface>hardware_interface/EffortJointInterface</hardwareInterface>
            </joint>
        </transmission>
    </xacro:macro>

    
    <link name="world" />
    <xacro:add_link link_name="base_link" ori_xyz="${base_ori_xyz}" mass="${base_mass}" ixx="${base_ixx}" ixy="${base_ixy}" ixz="${base_ixz}" iyy="${base_iyy}" iyz="${base_iyz}" izz="${base_izz}" flag="1" rgba="${base_rgba}" />
    <xacro:add_link link_name="r_arm_Link1" ori_xyz="${link1_r_ori_xyz}" mass="${link1_mass}" ixx="${link1_ixx}" ixy="${link1_ixy}" ixz="${link1_ixz}" iyy="${link1_iyy}" iyz="${link1_iyz}" izz="${link1_izz}" flag="1" rgba="${link_rgba}" />
    <xacro:add_link link_name="r_arm_Link2" ori_xyz="${link2_r_ori_xyz}" mass="${link2_mass}" ixx="${link2_ixx}" ixy="${link2_ixy}" ixz="${link2_ixz}" iyy="${link2_iyy}" iyz="${link2_iyz}" izz="${link2_izz}" flag="1" rgba="${link_rgba}" />
    <xacro:add_link link_name="r_arm_Link3" ori_xyz="${link3_r_ori_xyz}" mass="${link3_mass}" ixx="${link3_ixx}" ixy="${link3_ixy}" ixz="${link3_ixz}" iyy="${link3_iyy}" iyz="${link3_iyz}" izz="${link3_izz}" flag="1" rgba="${link_rgba}" />
    <xacro:add_link link_name="r_arm_Link4" ori_xyz="${link4_r_ori_xyz}" mass="${link4_mass}" ixx="${link4_ixx}" ixy="${link4_ixy}" ixz="${link4_ixz}" iyy="${link4_iyy}" iyz="${link4_iyz}" izz="${link4_izz}" flag="1" rgba="${link_rgba}" />
    <xacro:add_link link_name="r_arm_Link5" ori_xyz="${link5_r_ori_xyz}" mass="${link5_mass}" ixx="${link5_ixx}" ixy="${link5_ixy}" ixz="${link5_ixz}" iyy="${link5_iyy}" iyz="${link5_iyz}" izz="${link5_izz}" flag="1" rgba="${link_rgba}" />
    <xacro:add_link link_name="r_arm_Link6" ori_xyz="${link6_r_ori_xyz}" mass="${link6_mass}" ixx="${link6_ixx}" ixy="${link6_ixy}" ixz="${link6_ixz}" iyy="${link6_iyy}" iyz="${link6_iyz}" izz="${link6_izz}" flag="1" rgba="${link_rgba}" />
    <xacro:add_link link_name="r_arm_Link7" ori_xyz="${link7_r_ori_xyz}" mass="${link7_mass}" ixx="${link7_ixx}" ixy="${link7_ixy}" ixz="${link7_ixz}" iyy="${link7_iyy}" iyz="${link7_iyz}" izz="${link7_izz}" flag="1" rgba="${link_rgba}" />
    <xacro:add_link link_name="l_arm_Link1" ori_xyz="${link1_l_ori_xyz}" mass="${link1_mass}" ixx="${link1_ixx}" ixy="${link1_ixy}" ixz="${link1_ixz}" iyy="${link1_iyy}" iyz="${link1_iyz}" izz="${link1_izz}" flag="-1" rgba="${link_rgba}" />
    <xacro:add_link link_name="l_arm_Link2" ori_xyz="${link2_l_ori_xyz}" mass="${link2_mass}" ixx="${link2_ixx}" ixy="${link2_ixy}" ixz="${link2_ixz}" iyy="${link2_iyy}" iyz="${link2_iyz}" izz="${link2_izz}" flag="-1" rgba="${link_rgba}" />
    <xacro:add_link link_name="l_arm_Link3" ori_xyz="${link3_l_ori_xyz}" mass="${link3_mass}" ixx="${link3_ixx}" ixy="${link3_ixy}" ixz="${link3_ixz}" iyy="${link3_iyy}" iyz="${link3_iyz}" izz="${link3_izz}" flag="-1" rgba="${link_rgba}" />
    <xacro:add_link link_name="l_arm_Link4" ori_xyz="${link4_l_ori_xyz}" mass="${link4_mass}" ixx="${link4_ixx}" ixy="${link4_ixy}" ixz="${link4_ixz}" iyy="${link4_iyy}" iyz="${link4_iyz}" izz="${link4_izz}" flag="-1" rgba="${link_rgba}" />
    <xacro:add_link link_name="l_arm_Link5" ori_xyz="${link5_l_ori_xyz}" mass="${link5_mass}" ixx="${link5_ixx}" ixy="${link5_ixy}" ixz="${link5_ixz}" iyy="${link5_iyy}" iyz="${link5_iyz}" izz="${link5_izz}" flag="-1" rgba="${link_rgba}" />
    <xacro:add_link link_name="l_arm_Link6" ori_xyz="${link6_l_ori_xyz}" mass="${link6_mass}" ixx="${link6_ixx}" ixy="${link6_ixy}" ixz="${link6_ixz}" iyy="${link6_iyy}" iyz="${link6_iyz}" izz="${link6_izz}" flag="-1" rgba="${link_rgba}" />
    <xacro:add_link link_name="l_arm_Link7" ori_xyz="${link7_l_ori_xyz}" mass="${link7_mass}" ixx="${link7_ixx}" ixy="${link7_ixy}" ixz="${link7_ixz}" iyy="${link7_iyy}" iyz="${link7_iyz}" izz="${link7_izz}" flag="-1" rgba="${link_rgba}" />
    <link name="r_tool" />
    <link name="l_tool" />

    <joint name="world2base" type="fixed">
        <parent link="world"/>
        <child link="base_link"/>
        <origin 
        xyz="0 0 0.8" 
        rpy="${PI/2} 0 0" />
    </joint>
    <xacro:add_joint joint_name="r_arm_Joint1" parent_link="base_link" child_link="r_arm_Link1" origin_xyz="${joint1_r_ori_xyz}" origin_rpy="${joint1_r_ori_rpy}" />
    <xacro:add_joint joint_name="r_arm_Joint2" parent_link="r_arm_Link1" child_link="r_arm_Link2" origin_xyz="${joint2_r_ori_xyz}" origin_rpy="${joint2_r_ori_rpy}" />
    <xacro:add_joint joint_name="r_arm_Joint3" parent_link="r_arm_Link2" child_link="r_arm_Link3" origin_xyz="${joint3_r_ori_xyz}" origin_rpy="${joint3_r_ori_rpy}" />
    <xacro:add_joint joint_name="r_arm_Joint4" parent_link="r_arm_Link3" child_link="r_arm_Link4" origin_xyz="${joint4_r_ori_xyz}" origin_rpy="${joint4_r_ori_rpy}" />
    <xacro:add_joint joint_name="r_arm_Joint5" parent_link="r_arm_Link4" child_link="r_arm_Link5" origin_xyz="${joint5_r_ori_xyz}" origin_rpy="${joint5_r_ori_rpy}" />
    <xacro:add_joint joint_name="r_arm_Joint6" parent_link="r_arm_Link5" child_link="r_arm_Link6" origin_xyz="${joint6_r_ori_xyz}" origin_rpy="${joint6_r_ori_rpy}" />
    <xacro:add_joint joint_name="r_arm_Joint7" parent_link="r_arm_Link6" child_link="r_arm_Link7" origin_xyz="${joint7_r_ori_xyz}" origin_rpy="${joint7_r_ori_rpy}" />
    <xacro:add_joint joint_name="l_arm_Joint1" parent_link="base_link" child_link="l_arm_Link1" origin_xyz="${joint1_l_ori_xyz}" origin_rpy="${joint1_l_ori_rpy}" />
    <xacro:add_joint joint_name="l_arm_Joint2" parent_link="l_arm_Link1" child_link="l_arm_Link2" origin_xyz="${joint2_l_ori_xyz}" origin_rpy="${joint2_l_ori_rpy}" />
    <xacro:add_joint joint_name="l_arm_Joint3" parent_link="l_arm_Link2" child_link="l_arm_Link3" origin_xyz="${joint3_l_ori_xyz}" origin_rpy="${joint3_l_ori_rpy}" />
    <xacro:add_joint joint_name="l_arm_Joint4" parent_link="l_arm_Link3" child_link="l_arm_Link4" origin_xyz="${joint4_l_ori_xyz}" origin_rpy="${joint4_l_ori_rpy}" />
    <xacro:add_joint joint_name="l_arm_Joint5" parent_link="l_arm_Link4" child_link="l_arm_Link5" origin_xyz="${joint5_l_ori_xyz}" origin_rpy="${joint5_l_ori_rpy}" />
    <xacro:add_joint joint_name="l_arm_Joint6" parent_link="l_arm_Link5" child_link="l_arm_Link6" origin_xyz="${joint6_l_ori_xyz}" origin_rpy="${joint6_l_ori_rpy}" />
    <xacro:add_joint joint_name="l_arm_Joint7" parent_link="l_arm_Link6" child_link="l_arm_Link7" origin_xyz="${joint7_l_ori_xyz}" origin_rpy="${joint7_l_ori_rpy}" />
        
    <xacro:add_trans joint_name="r_arm_Joint1" />
    <xacro:add_trans joint_name="r_arm_Joint2" />
    <xacro:add_trans joint_name="r_arm_Joint3" />
    <xacro:add_trans joint_name="r_arm_Joint4" />
    <xacro:add_trans joint_name="r_arm_Joint5" />
    <xacro:add_trans joint_name="r_arm_Joint6" />
    <xacro:add_trans joint_name="r_arm_Joint7" />
    <xacro:add_trans joint_name="l_arm_Joint1" />
    <xacro:add_trans joint_name="l_arm_Joint2" />
    <xacro:add_trans joint_name="l_arm_Joint3" />
    <xacro:add_trans joint_name="l_arm_Joint4" />
    <xacro:add_trans joint_name="l_arm_Joint5" />
    <xacro:add_trans joint_name="l_arm_Joint6" />
    <xacro:add_trans joint_name="l_arm_Joint7" />

    <joint name="r_jnt2tool" type="fixed">
        <parent link="r_arm_Link7"/>
        <child link="r_tool"/>
        <origin 
        xyz="0.05 0 -0.0295" 
        rpy="0 0 0" />
    </joint>
    <joint name="l_jnt2tool" type="fixed">
        <parent link="l_arm_Link7"/>
        <child link="l_tool"/>
        <origin 
        xyz="0.05 0 0.0295" 
        rpy="0 0 0" />
    </joint>

    <gazebo>
        <plugin name="gazebo_ros_control" filename="libgazebo_ros_control.so">
            <robotNamespace>Arms_Gen2</robotNamespace>
        </plugin>
    </gazebo>
</robot>