<?xml version="1.0" encoding="utf-8"?>
<robot
  name="<PERSON><PERSON><PERSON>g_Arm_Gen3_with_Hand">
  <link name="footprint">
    <visual>
      <origin xyz="0 0 0" rpy="0 0 0" />
      <geometry>
        <box size="0.1 0.1 0.1" />
      </geometry>
      <material name="">
        <color rgba="0 0 0 0" />
      </material>
    </visual>
  </link>
  <link name="base_link">
    <inertial>
      <origin
        xyz="-0.0137906099453444 0.00103375326981413 -0.563088704955717"
        rpy="0 0 0" />
      <mass
        value="54.770449857177" />
      <inertia
        ixx="0.255174678928375"
        ixy="-2.02065579522071E-05"
        ixz="-0.00334466506072113"
        iyy="0.233021441171162"
        iyz="-6.67680140743922E-05"
        izz="0.135494765980168" />
    </inertial>
    <visual>
      <origin
        xyz="0 0 0"
        rpy="0 0 0" />
      <geometry>
        <mesh
          filename="package://robot_controller/description/meshes_arms_gen3/base_link.STL" />
      </geometry>
      <material
        name="">
        <color
          rgba="0.752941176470588 0.752941176470588 0.752941176470588 1" />
      </material>
    </visual>
    <collision>
      <origin
        xyz="0 0 0"
        rpy="0 0 0" />
      <geometry>
        <mesh
          filename="package://robot_controller/description/meshes_arms_gen3/base_link.STL" />
      </geometry>
    </collision>
  </link>
  <joint name="base_joint"
    type="fixed">
    <origin
      xyz="0 0 1.45"
      rpy="0 0 0" />
    <parent
      link="footprint" />
    <child
      link="base_link" />
  </joint>
  <link name="AR1">
    <inertial>
      <origin
        xyz="0.0083594 9.2398E-05 0.062254"
        rpy="0 0 0" />
      <mass
        value="1.0518" />
      <inertia
        ixx="0.00093105"
        ixy="5.3362E-06"
        ixz="1.9958E-05"
        iyy="0.001001"
        iyz="-9.6291E-07"
        izz="0.00090721" />
    </inertial>
    <visual>
      <origin
        xyz="0 0 0"
        rpy="0 0 0" />
      <geometry>
        <mesh
          filename="package://robot_controller/description/meshes_arms_gen3/AR1.STL" />
      </geometry>
      <material
        name="">
        <color
          rgba="0.89804 0.91765 0.92941 1" />
      </material>
    </visual>
    <collision>
      <origin
        xyz="0 0 0"
        rpy="0 0 0" />
      <geometry>
        <mesh
          filename="package://robot_controller/description/meshes_arms_gen3/AR1.STL" />
      </geometry>
    </collision>
  </link>
  <joint name="AJR1"
    type="revolute">
    <origin
      xyz="0 -0.193 0.055"
      rpy="1.5708 0 0" />
    <parent
      link="base_link" />
    <child
      link="AR1" />
    <axis
      xyz="0 0 1" />
    <limit lower="-3.14" upper="3.14" effort="100" velocity="1" />
  </joint>
  <link name="AR2">
    <inertial>
      <origin
        xyz="0.031266 -8.259E-06 -0.050803"
        rpy="0 0 0" />
      <mass
        value="0.28432" />
      <inertia
        ixx="0.00034291"
        ixy="-1.583E-08"
        ixz="6.9476E-05"
        iyy="0.00040808"
        iyz="4.9468E-09"
        izz="0.00036954" />
    </inertial>
    <visual>
      <origin
        xyz="0 0 0"
        rpy="0 0 0" />
      <geometry>
        <mesh
          filename="package://robot_controller/description/meshes_arms_gen3/AR2.STL" />
      </geometry>
      <material
        name="">
        <color
          rgba="0.89804 0.91765 0.92941 1" />
      </material>
    </visual>
    <collision>
      <origin
        xyz="0 0 0"
        rpy="0 0 0" />
      <geometry>
        <mesh
          filename="package://robot_controller/description/meshes_arms_gen3/AR2.STL" />
      </geometry>
    </collision>
  </link>
  <joint name="AJR2"
    type="revolute">
    <origin
      xyz="0.0565 0 0.067"
      rpy="-1.5708 0 -1.5708" />
    <parent
      link="AR1" />
    <child
      link="AR2" />
    <axis
      xyz="0 0 1" />
    <limit lower="-2.356194490192345" upper="0.08726646259971647" effort="100" velocity="1" />
  </joint>
  <link name="AR3">
    <inertial>
      <origin
        xyz="-0.0033232 3.5802E-06 0.1286"
        rpy="0 0 0" />
      <mass
        value="2.3857" />
      <inertia
        ixx="0.0022934"
        ixy="-3.7749E-06"
        ixz="-2.9436E-05"
        iyy="0.0024369"
        iyz="-4.0989E-06"
        izz="0.0016228" />
    </inertial>
    <visual>
      <origin
        xyz="0 0 0"
        rpy="0 0 0" />
      <geometry>
        <mesh
          filename="package://robot_controller/description/meshes_arms_gen3/AR3.STL" />
      </geometry>
      <material
        name="">
        <color
          rgba="0.64706 0.61961 0.58824 1" />
      </material>
    </visual>
    <collision>
      <origin
        xyz="0 0 0"
        rpy="0 0 0" />
      <geometry>
        <mesh
          filename="package://robot_controller/description/meshes_arms_gen3/AR3.STL" />
      </geometry>
    </collision>
  </link>
  <joint name="AJR3"
    type="revolute">
    <origin
      xyz="0.059 0 -0.0565"
      rpy="1.5708 0 1.5708" />
    <parent
      link="AR2" />
    <child
      link="AR3" />
    <axis
      xyz="0 0 1" />
    <limit lower="-1.5882496" upper="1.5882496" effort="100" velocity="1" />
  </joint>
  <link name="AR4">
    <inertial>
      <origin
        xyz="-0.00058026 -0.094709 -0.042737"
        rpy="0 0 0" />
      <mass
        value="1.2408" />
      <inertia
        ixx="0.0008869"
        ixy="4.2756E-06"
        ixz="-9.5056E-07"
        iyy="0.00054498"
        iyz="-2.4428E-05"
        izz="0.00085884" />
    </inertial>
    <visual>
      <origin
        xyz="0 0 0"
        rpy="0 0 0" />
      <geometry>
        <mesh
          filename="package://robot_controller/description/meshes_arms_gen3/AR4.STL" />
      </geometry>
      <material
        name="">
        <color
          rgba="0.89804 0.91765 0.92941 1" />
      </material>
    </visual>
    <collision>
      <origin
        xyz="0 0 0"
        rpy="0 0 0" />
      <geometry>
        <mesh
          filename="package://robot_controller/description/meshes_arms_gen3/AR4.STL" />
      </geometry>
    </collision>
  </link>
  <joint name="AJR4"
    type="revolute">
    <origin
      xyz="-0.0424 0 0.186"
      rpy="-1.5708 0 1.5708" />
    <parent
      link="AR3" />
    <child
      link="AR4" />
    <axis
      xyz="0 0 1" />
    <limit lower="-0.0174532925199433" upper="2.00712864" effort="100" velocity="1" />
  </joint>
  <link name="AR5">
    <inertial>
      <origin
        xyz="-1.803E-05 0.0011913 0.034704"
        rpy="0 0 0" />
      <mass
        value="0.99941" />
      <inertia
        ixx="0.00046967"
        ixy="-5.4165E-07"
        ixz="-5.0766E-07"
        iyy="0.00038226"
        iyz="-1.8522E-06"
        izz="0.00046884" />
    </inertial>
    <visual>
      <origin
        xyz="0 0 0"
        rpy="0 0 0" />
      <geometry>
        <mesh
          filename="package://robot_controller/description/meshes_arms_gen3/AR5.STL" />
      </geometry>
      <material
        name="">
        <color
          rgba="0.89804 0.91765 0.92941 1" />
      </material>
    </visual>
    <collision>
      <origin
        xyz="0 0 0"
        rpy="0 0 0" />
      <geometry>
        <mesh
          filename="package://robot_controller/description/meshes_arms_gen3/AR5.STL" />
      </geometry>
    </collision>
  </link>
  <joint name="AJR5"
    type="revolute">
    <origin
      xyz="0 -0.14 -0.0424"
      rpy="1.5708 0 0" />
    <parent
      link="AR4" />
    <child
      link="AR5" />
    <axis
      xyz="0 0 1" />
    <limit lower="-3.14" upper="3.14" effort="100" velocity="1" />
  </joint>
  <link name="AR6">
    <inertial>
      <origin
        xyz="0.0013138 -0.06797 -0.033214"
        rpy="0 0 0" />
      <mass
        value="1.0311" />
      <inertia
        ixx="0.00038835"
        ixy="2.3961E-07"
        ixz="-7.225E-09"
        iyy="0.00046996"
        iyz="-8.9045E-06"
        izz="0.00048276" />
    </inertial>
    <visual>
      <origin
        xyz="0 0 0"
        rpy="0 0 0" />
      <geometry>
        <mesh
          filename="package://robot_controller/description/meshes_arms_gen3/AR6.STL" />
      </geometry>
      <material
        name="">
        <color
          rgba="0.89804 0.91765 0.92941 1" />
      </material>
    </visual>
    <collision>
      <origin
        xyz="0 0 0"
        rpy="0 0 0" />
      <geometry>
        <mesh
          filename="package://robot_controller/description/meshes_arms_gen3/AR6.STL" />
      </geometry>
    </collision>
  </link>
  <joint name="AJR6"
    type="revolute">
    <origin
      xyz="0 0.0345 0.035"
      rpy="-1.5708 0 0" />
    <parent
      link="AR5" />
    <child
      link="AR6" />
    <axis
      xyz="0 0 1" />
    <limit lower="-1.5707963267949" upper="1.5707963267949" effort="100" velocity="1" />
  </joint>
  
  <link name="r_hand_base_Link">
      <inertial>
      <origin
          xyz="-0.068046 -0.00089687 -0.03139"
          rpy="0 0 0" />
      <mass
          value="0.16133" />
      <inertia
          ixx="9.227E-05"
          ixy="3.175E-06"
          ixz="-1.0576E-05"
          iyy="0.000146"
          iyz="-1.4727E-06"
          izz="0.00010833" />
      </inertial>
      <visual>
      <origin
          xyz="0 0 0"
          rpy="0 0 3.1415926" />
      <geometry>
          <mesh
          filename="package://robot_controller//description/meshes_arms_gen2/R_hand_base_link.STL" />
      </geometry>
      <material
          name="">
          <color
          rgba="0.89804 0.91765 0.92941 1" />
      </material>
      </visual>
      <collision>
      <origin
          xyz="0 0 0"
          rpy="0 0 0" />
      <geometry>
          <mesh
          filename="package://robot_controller//description/meshes_arms_gen2/R_hand_base_link.STL" />
      </geometry>
      </collision>
  </link>

  <joint
    name="AJR7"
    type="revolute">
    <origin
      xyz="0.0345 -0.07 -0.0345"
      rpy="-1.5708 0 -1.5708" />
    <parent
      link="AR6" />
    <child
      link="r_hand_base_Link" />
    <axis
      xyz="0 0 1" />
    <limit lower="-1.5707963267949" upper="1.5707963267949" effort="100" velocity="1" />
  </joint>

  <link name="r_thumb_proximal_base">
      <inertial>
      <origin
          xyz="-0.0048193 -0.00086977 -0.00757"
          rpy="0 0 0" />
      <mass
          value="0.0018869" />
      <inertia
          ixx="5.1093E-08"
          ixy="3.6091E-09"
          ixz="4.8645E-09"
          iyy="8.6228E-08"
          iyz="-1.1549E-10"
          izz="6.7433E-08" />
      </inertial>
      <visual>
      <origin
          xyz="0 0 0"
          rpy="0 0 0" />
      <geometry>
          <mesh
          filename="package://robot_controller//description/meshes_arms_gen2/R_thumb_proximal_base.STL" />
      </geometry>
      <material
          name="">
          <color
          rgba="0.89804 0.91765 0.92941 1" />
      </material>
      </visual>
      <collision>
      <origin
          xyz="0 0 0"
          rpy="0 0 0" />
      <geometry>
          <mesh
          filename="package://robot_controller//description/meshes_arms_gen2/R_thumb_proximal_base.STL" />
      </geometry>
      </collision>
  </link>
  <joint name="r_hand_Joint1" type="revolute">
      <origin
      xyz="0.0844 0.01496 -0.01705"
      rpy="0 1.5708 0" />
      <parent
      link="r_hand_base_Link" />
      <child
      link="r_thumb_proximal_base" />
      <axis
      xyz="0 0 -1" />
      <limit
      lower="-0.1"
      upper="1.3"
      effort="1"
      velocity="0.5" />
  </joint>
  <link name="r_thumb_proximal">
      <inertial>
      <origin
          xyz="-0.0045008 0.024988 -0.0080376"
          rpy="0 0 0" />
      <mass
          value="0.0066085" />
      <inertia
          ixx="2.2204E-06"
          ixy="5.4582E-07"
          ixz="1.338E-09"
          iyy="1.0849E-06"
          iyz="3.7768E-10"
          izz="2.7864E-06" />
      </inertial>
      <visual>
      <origin
          xyz="0 0 0"
          rpy="0 0 0" />
      <geometry>
          <mesh
          filename="package://robot_controller//description/meshes_arms_gen2/R_thumb_proximal.STL" />
      </geometry>
      <material
          name="">
          <color
          rgba="0.89804 0.91765 0.92941 1" />
      </material>
      </visual>
      <collision>
      <origin
          xyz="0 0 0"
          rpy="0 0 0" />
      <geometry>
          <mesh
          filename="package://robot_controller//description/meshes_arms_gen2/R_thumb_proximal.STL" />
      </geometry>
      </collision>
  </link>
  <joint name="r_hand_Joint2" type="revolute">
      <origin
      xyz="-0.012162 0.0069529 -0.00925"
      rpy="1.5708 1.1727 -3.0531" />
      <parent
      link="r_thumb_proximal_base" />
      <child
      link="r_thumb_proximal" />
      <axis
      xyz="0 0 1" />
      <limit
      lower="-0.1"
      upper="0.6"
      effort="1"
      velocity="0.5" />
  </joint>
  <link name="r_thumb_intermediate">
      <inertial>
      <origin
          xyz="0.0072858 -0.0065352 -0.0070692"
          rpy="0 0 0" />
      <mass
          value="0.0037844" />
      <inertia
          ixx="2.6677E-07"
          ixy="-6.1796E-08"
          ixz="-3.5949E-09"
          iyy="4.2702E-07"
          iyz="-1.1225E-09"
          izz="4.6522E-07" />
      </inertial>
      <visual>
      <origin
          xyz="0 0 0"
          rpy="0 0 0" />
      <geometry>
          <mesh
          filename="package://robot_controller//description/meshes_arms_gen2/R_thumb_intermediate.STL" />
      </geometry>
      <material
          name="">
          <color
          rgba="0.89804 0.91765 0.92941 1" />
      </material>
      </visual>
      <collision>
      <origin
          xyz="0 0 0"
          rpy="0 0 0" />
      <geometry>
          <mesh
          filename="package://robot_controller//description/meshes_arms_gen2/R_thumb_intermediate.STL" />
      </geometry>
      </collision>
  </link>
  <joint name="r_hand_Joint2_mimic1" type="revolute">
      <origin
      xyz="-0.017367 0.05324 -0.0008"
      rpy="0 0.018104 1.744" />
      <parent
      link="r_thumb_proximal" />
      <child
      link="r_thumb_intermediate" />
      <axis
      xyz="-0.018103 0 0.99984" />
      <limit
      lower="0"
      upper="0.8"
      effort="1"
      velocity="0.5" />
      <mimic
      joint="r_hand_Joint2"
      multiplier="1.6"
      offset="0" />
  </joint>
  <link name="r_thumb_distal">
      <inertial>
      <origin
          xyz="0.011533 -0.0016554 -0.0033306"
          rpy="0 0 0" />
      <mass
          value="0.0033441" />
      <inertia
          ixx="9.0027E-08"
          ixy="-1.3011E-08"
          ixz="-2.7424E-08"
          iyy="1.9365E-07"
          iyz="-3.2623E-09"
          izz="1.9344E-07" />
      </inertial>
      <visual>
      <origin
          xyz="0 0 0"
          rpy="0 0 0" />
      <geometry>
          <mesh
          filename="package://robot_controller//description/meshes_arms_gen2/R_thumb_distal.STL" />
      </geometry>
      <material
          name="">
          <color
          rgba="0.89804 0.91765 0.92941 1" />
      </material>
      </visual>
      <collision>
      <origin
          xyz="0 0 0"
          rpy="0 0 0" />
      <geometry>
          <mesh
          filename="package://robot_controller//description/meshes_arms_gen2/R_thumb_distal.STL" />
      </geometry>
      </collision>
  </link>
  <joint name="r_hand_Joint2_mimic2" type="revolute">
      <origin
      xyz="0.022543 -0.0024047 -0.00079204"
      rpy="-0.0014192 0.22451 0.076172" />
      <parent
      link="r_thumb_intermediate" />
      <child
      link="r_thumb_distal" />
      <axis
      xyz="-0.24019 0 0.97073" />
      <limit
      lower="0"
      upper="1.2"
      effort="1"
      velocity="0.5" />
      <mimic
      joint="r_hand_Joint2"
      multiplier="2.4"
      offset="0" />
  </joint>
  <link name="r_thumb_tip" />
  <joint name="r_set_thumb_tip" type="fixed">
      <parent link="r_thumb_distal"/>
      <child link="r_thumb_tip"/>
      <origin 
      xyz="0.017 0.006 -0.002" 
      rpy="0 -0.27 0" />
  </joint>

  <link name="r_index_proximal">
      <inertial>
      <origin
          xyz="0.011935 -0.001284 -0.0060001"
          rpy="0 0 0" />
      <mass
          value="0.0042403" />
      <inertia
          ixx="2.1163E-07"
          ixy="1.7969E-08"
          ixz="-5.1832E-12"
          iyy="6.6216E-07"
          iyz="-1.8291E-12"
          izz="6.9398E-07" />
      </inertial>
      <visual>
      <origin
          xyz="0 0 0"
          rpy="0 0 0" />
      <geometry>
          <mesh
          filename="package://robot_controller//description/meshes_arms_gen2/R_index_proximal.STL" />
      </geometry>
      <material
          name="">
          <color
          rgba="0.89804 0.91765 0.92941 1" />
      </material>
      </visual>
      <collision>
      <origin
          xyz="0 0 0"
          rpy="0 0 0" />
      <geometry>
          <mesh
          filename="package://robot_controller//description/meshes_arms_gen2/R_index_proximal.STL" />
      </geometry>
      </collision>
  </link>
  <joint name="r_hand_Joint3" type="revolute">
      <origin
      xyz="0.15183 -0.0022853 -0.0052323"
      rpy="0 -0.034907 0" />
      <parent
      link="r_hand_base_Link" />
      <child
      link="r_index_proximal" />
      <axis
      xyz="0 0 1" />
      <limit
      lower="0"
      upper="1.7"
      effort="1"
      velocity="0.5" />
  </joint>
  <link name="r_index_distal">
      <inertial>
      <origin
          xyz="0.020025 -0.0019274 -0.00281"
          rpy="0 0 0" />
      <mass
          value="0.0045683" />
      <inertia
          ixx="1.0478E-07"
          ixy="-8.853E-08"
          ixz="-7.5074E-08"
          iyy="7.6073E-07"
          iyz="-9.8169E-09"
          izz="7.7347E-07" />
      </inertial>
      <visual>
      <origin
          xyz="0 0 0"
          rpy="0 0 0" />
      <geometry>
          <mesh
          filename="package://robot_controller//description/meshes_arms_gen2/R_index_distal.STL" />
      </geometry>
      <material
          name="">
          <color
          rgba="0.89804 0.91765 0.92941 1" />
      </material>
      </visual>
      <collision>
      <origin
          xyz="0 0 0"
          rpy="0 0 0" />
      <geometry>
          <mesh
          filename="package://robot_controller//description/meshes_arms_gen2/R_index_distal.STL" />
      </geometry>
      </collision>
  </link>
  <joint name="r_hand_Joint3_mimic" type="revolute">
      <origin
      xyz="0.032038 0.0024579 -0.001"
      rpy="0 0.11044 -0.0069662" />
      <parent
      link="r_index_proximal" />
      <child
      link="r_index_distal" />
      <axis
      xyz="0.11022 0 0.99391" />
      <limit
      lower="0"
      upper="1.7"
      effort="1"
      velocity="0.5" />
      <mimic
      joint="r_hand_Joint3"
      multiplier="1"
      offset="0" />
  </joint>
  <link name="r_index_tip" />
  <joint name="r_set_index_tip" type="fixed">
      <parent link="r_index_distal"/>
      <child link="r_index_tip"/>
      <origin 
      xyz="0.042 0.005 -0.00" 
      rpy="0 0 0" />
  </joint>
  <link name="r_middle_proximal">
      <inertial>
      <origin
          xyz="0.011934 -0.001297 -0.0060001"
          rpy="0 0 0" />
      <mass
          value="0.0042403" />
      <inertia
          ixx="2.1167E-07"
          ixy="1.8461E-08"
          ixz="-6.6873E-12"
          iyy="6.6211E-07"
          iyz="-1.7995E-12"
          izz="6.9397E-07" />
      </inertial>
      <visual>
      <origin
          xyz="0 0 0"
          rpy="0 0 0" />
      <geometry>
          <mesh
          filename="package://robot_controller//description/meshes_arms_gen2/R_middle_proximal.STL" />
      </geometry>
      <material
          name="">
          <color
          rgba="0.89804 0.91765 0.92941 1" />
      </material>
      </visual>
      <collision>
      <origin
          xyz="0 0 0"
          rpy="0 0 0" />
      <geometry>
          <mesh
          filename="package://robot_controller//description/meshes_arms_gen2/R_middle_proximal.STL" />
      </geometry>
      </collision>
  </link>
  <joint name="r_hand_Joint4" type="revolute">
      <origin
      xyz="0.1524 -0.0022853 -0.02455"
      rpy="0 0 0" />
      <parent
      link="r_hand_base_Link" />
      <child
      link="r_middle_proximal" />
      <axis
      xyz="0 0 1" />
      <limit
      lower="0"
      upper="1.7"
      effort="1"
      velocity="0.5" />
  </joint>
  <link name="r_middle_distal">
      <inertial>
      <origin
          xyz="0.021091 -0.0028652 -0.0028545"
          rpy="0 0 0" />
      <mass
          value="0.0050396" />
      <inertia
          ixx="1.0713E-07"
          ixy="-7.4664E-08"
          ixz="-9.0274E-08"
          iyy="9.6684E-07"
          iyz="-7.6864E-09"
          izz="9.7455E-07" />
      </inertial>
      <visual>
      <origin
          xyz="0 0 0"
          rpy="0 0 0" />
      <geometry>
          <mesh
          filename="package://robot_controller//description/meshes_arms_gen2/R_middle_distal.STL" />
      </geometry>
      <material
          name="">
          <color
          rgba="0.89804 0.91765 0.92941 1" />
      </material>
      </visual>
      <collision>
      <origin
          xyz="0 0 0"
          rpy="0 0 0" />
      <geometry>
          <mesh
          filename="package://robot_controller//description/meshes_arms_gen2/R_middle_distal.STL" />
      </geometry>
      </collision>
  </link>
  <joint name="r_hand_Joint4_mimic" type="revolute">
      <origin
      xyz="0.032041 0.0024229 -0.001"
      rpy="0 0.10262 0.045516" />
      <parent
      link="r_middle_proximal" />
      <child
      link="r_middle_distal" />
      <axis
      xyz="-0.10244 0 0.99474" />
      <limit
      lower="0"
      upper="1.7"
      effort="1"
      velocity="0.5" />
      <mimic
      joint="r_hand_Joint4"
      multiplier="1"
      offset="0" />
  </joint>
  <link name="r_ring_proximal">
      <inertial>
      <origin
          xyz="0.011934 -0.001297 -0.0060002"
          rpy="0 0 0" />
      <mass
          value="0.0042403" />
      <inertia
          ixx="2.1167E-07"
          ixy="1.8461E-08"
          ixz="-6.6696E-12"
          iyy="6.6211E-07"
          iyz="-1.5826E-12"
          izz="6.9397E-07" />
      </inertial>
      <visual>
      <origin
          xyz="0 0 0"
          rpy="0 0 0" />
      <geometry>
          <mesh
          filename="package://robot_controller//description/meshes_arms_gen2/R_ring_proximal.STL" />
      </geometry>
      <material
          name="">
          <color
          rgba="0.89804 0.91765 0.92941 1" />
      </material>
      </visual>
      <collision>
      <origin
          xyz="0 0 0"
          rpy="0 0 0" />
      <geometry>
          <mesh
          filename="package://robot_controller//description/meshes_arms_gen2/R_ring_proximal.STL" />
      </geometry>
      </collision>
  </link>
  <joint name="r_hand_Joint5" type="revolute">
      <origin
      xyz="0.15221 -0.0022853 -0.043787"
      rpy="0 0.05236 0" />
      <parent
      link="r_hand_base_Link" />
      <child
      link="r_ring_proximal" />
      <axis
      xyz="0 0 1" />
      <limit
      lower="0"
      upper="1.7"
      effort="1"
      velocity="0.5" />
  </joint>
  <link name="r_ring_distal">
      <inertial>
      <origin
          xyz="0.019918 -0.0028281 -0.002811"
          rpy="0 0 0" />
      <mass
          value="0.0045683" />
      <inertia
          ixx="9.8143E-08"
          ixy="-5.8397E-08"
          ixz="-7.5623E-08"
          iyy="7.6739E-07"
          iyz="-6.4253E-09"
          izz="7.7341E-07" />
      </inertial>
      <visual>
      <origin
          xyz="0 0 0"
          rpy="0 0 0" />
      <geometry>
          <mesh
          filename="package://robot_controller//description/meshes_arms_gen2/R_ring_distal.STL" />
      </geometry>
      <material
          name="">
          <color
          rgba="0.89804 0.91765 0.92941 1" />
      </material>
      </visual>
      <collision>
      <origin
          xyz="0 0 0"
          rpy="0 0 0" />
      <geometry>
          <mesh
          filename="package://robot_controller//description/meshes_arms_gen2/R_ring_distal.STL" />
      </geometry>
      </collision>
  </link>
  <joint name="r_hand_Joint5_mimic" type="revolute">
      <origin
      xyz="0.032041 0.0024229 -0.001"
      rpy="0.003629 0.11048 0.032903" />
      <parent
      link="r_ring_proximal" />
      <child
      link="r_ring_distal" />
      <axis
      xyz="0.11025 -0.0036068 0.9939" />
      <limit
      lower="0"
      upper="1.7"
      effort="1"
      velocity="0.5" />
      <mimic
      joint="r_hand_Joint5"
      multiplier="1"
      offset="0" />
  </joint>
  <link name="r_pinky_prximal">
      <inertial>
      <origin
          xyz="0.011934 -0.001297 -0.0060001"
          rpy="0 0 0" />
      <mass
          value="0.0042403" />
      <inertia
          ixx="2.1167E-07"
          ixy="1.8461E-08"
          ixz="-6.6789E-12"
          iyy="6.6211E-07"
          iyz="-1.6113E-12"
          izz="6.9398E-07" />
      </inertial>
      <visual>
      <origin
          xyz="0 0 0"
          rpy="0 0 0" />
      <geometry>
          <mesh
          filename="package://robot_controller//description/meshes_arms_gen2/R_pinky_prximal.STL" />
      </geometry>
      <material
          name="">
          <color
          rgba="0.89804 0.91765 0.92941 1" />
      </material>
      </visual>
      <collision>
      <origin
          xyz="0 0 0"
          rpy="0 0 0" />
      <geometry>
          <mesh
          filename="package://robot_controller//description/meshes_arms_gen2/R_pinky_prximal.STL" />
      </geometry>
      </collision>
  </link>
  <joint name="r_hand_Joint6" type="revolute">
      <origin
      xyz="0.15101 -0.0022853 -0.062988"
      rpy="0 0.10472 0" />
      <parent
      link="r_hand_base_Link" />
      <child
      link="r_pinky_prximal" />
      <axis
      xyz="0 0 1" />
      <limit
      lower="0"
      upper="1.7"
      effort="1"
      velocity="0.5" />
  </joint>
  <link name="r_pinky_distal">
      <inertial>
      <origin
          xyz="0.016693 -0.0026608 -0.0028207"
          rpy="0 0 0" />
      <mass
          value="0.0035996" />
      <inertia
          ixx="7.5956E-08"
          ixy="-3.7006E-08"
          ixz="-4.9515E-08"
          iyy="4.4E-07"
          iyz="-4.9527E-09"
          izz="4.421E-07" />
      </inertial>
      <visual>
      <origin
          xyz="0 0 0"
          rpy="0 0 0" />
      <geometry>
          <mesh
          filename="package://robot_controller//description/meshes_arms_gen2/R_pinky_distal.STL" />
      </geometry>
      <material
          name="">
          <color
          rgba="0.89804 0.91765 0.92941 1" />
      </material>
      </visual>
      <collision>
      <origin
          xyz="0 0 0"
          rpy="0 0 0" />
      <geometry>
          <mesh
          filename="package://robot_controller//description/meshes_arms_gen2/R_pinky_distal.STL" />
      </geometry>
      </collision>
  </link>
  <joint name="r_hand_Joint6_mimic" type="revolute">
      <origin
      xyz="0.032041 0.0024229 -0.001"
      rpy="0.0014827 0.13237 0.011233" />
      <parent
      link="r_pinky_prximal" />
      <child
      link="r_pinky_distal" />
      <axis
      xyz="0.13199 -0.0014697 0.99125" />
      <limit
      lower="0"
      upper="1.7"
      effort="1"
      velocity="0.5" />
      <mimic
      joint="r_hand_Joint6"
      multiplier="1"
      offset="0" />
  </joint>


  <link
    name="AL1">
    <inertial>
      <origin
        xyz="0.0083594 -9.2399E-05 -0.062254"
        rpy="0 0 0" />
      <mass
        value="1.0518" />
      <inertia
        ixx="0.00093105"
        ixy="-5.3361E-06"
        ixz="-1.9958E-05"
        iyy="0.001001"
        iyz="-9.6284E-07"
        izz="0.00090721" />
    </inertial>
    <visual>
      <origin
        xyz="0 0 0"
        rpy="0 0 0" />
      <geometry>
        <mesh
          filename="package://robot_controller/description/meshes_arms_gen3/AL1.STL" />
      </geometry>
      <material
        name="">
        <color
          rgba="0.89804 0.91765 0.92941 1" />
      </material>
    </visual>
    <collision>
      <origin
        xyz="0 0 0"
        rpy="0 0 0" />
      <geometry>
        <mesh
          filename="package://robot_controller/description/meshes_arms_gen3/AL1.STL" />
      </geometry>
    </collision>
  </link>
  <joint
    name="AJL1"
    type="revolute">
    <origin
      xyz="0 0.193 0.055"
      rpy="1.5708 0 0" />
    <parent
      link="base_link" />
    <child
      link="AL1" />
    <axis
      xyz="0 0 1" />
    <limit lower="-3.14" upper="3.14" effort="100" velocity="1" />
  </joint>
  <link
    name="AL2">
    <inertial>
      <origin
        xyz="0.031266 8.259E-06 0.050803"
        rpy="0 0 0" />
      <mass
        value="0.28432" />
      <inertia
        ixx="0.00034291"
        ixy="1.5831E-08"
        ixz="-6.9476E-05"
        iyy="0.00040808"
        iyz="4.947E-09"
        izz="0.00036954" />
    </inertial>
    <visual>
      <origin
        xyz="0 0 0"
        rpy="0 0 0" />
      <geometry>
        <mesh
          filename="package://robot_controller/description/meshes_arms_gen3/AL2.STL" />
      </geometry>
      <material
        name="">
        <color
          rgba="0.89804 0.91765 0.92941 1" />
      </material>
    </visual>
    <collision>
      <origin
        xyz="0 0 0"
        rpy="0 0 0" />
      <geometry>
        <mesh
          filename="package://robot_controller/description/meshes_arms_gen3/AL2.STL" />
      </geometry>
    </collision>
  </link>
  <joint
    name="AJL2"
    type="revolute">
    <origin
      xyz="0.0565 0 -0.067"
      rpy="1.5708 0 -1.5708" />
    <parent
      link="AL1" />
    <child
      link="AL2" />
    <axis
      xyz="0 0 1" />
    <limit lower="-2.356194490192345" upper="0.08726646259971647" effort="100" velocity="1" />
  </joint>
  <link
    name="AL3">
    <inertial>
      <origin
        xyz="-0.0033246 -7.7807E-05 -0.1286"
        rpy="0 0 0" />
      <mass
        value="2.3857" />
      <inertia
        ixx="0.0022934"
        ixy="3.7749E-06"
        ixz="2.9236E-05"
        iyy="0.0024369"
        iyz="-4.9066E-06"
        izz="0.0016228" />
    </inertial>
    <visual>
      <origin
        xyz="0 0 0"
        rpy="0 0 0" />
      <geometry>
        <mesh
          filename="package://robot_controller/description/meshes_arms_gen3/AL3.STL" />
      </geometry>
      <material
        name="">
        <color
          rgba="0.64706 0.61961 0.58824 1" />
      </material>
    </visual>
    <collision>
      <origin
        xyz="0 0 0"
        rpy="0 0 0" />
      <geometry>
        <mesh
          filename="package://robot_controller/description/meshes_arms_gen3/AL3.STL" />
      </geometry>
    </collision>
  </link>
  <joint
    name="AJL3"
    type="revolute">
    <origin
      xyz="0.059 0 0.0565"
      rpy="-1.5708 0 1.5708" />
    <parent
      link="AL2" />
    <child
      link="AL3" />
    <axis
      xyz="0 0 1" />
    <limit lower="-1.5882496" upper="1.5882496" effort="100" velocity="1" />
  </joint>
  <link
    name="AL4">
    <inertial>
      <origin
        xyz="-0.00058038 -0.094708 0.042703"
        rpy="0 0 0" />
      <mass
        value="1.2408" />
      <inertia
        ixx="0.00088684"
        ixy="4.2769E-06"
        ixz="1.6585E-06"
        iyy="0.000545"
        iyz="2.5663E-05"
        izz="0.0008587" />
    </inertial>
    <visual>
      <origin
        xyz="0 0 0"
        rpy="0 0 0" />
      <geometry>
        <mesh
          filename="package://robot_controller/description/meshes_arms_gen3/AL4.STL" />
      </geometry>
      <material
        name="">
        <color
          rgba="0.89804 0.91765 0.92941 1" />
      </material>
    </visual>
    <collision>
      <origin
        xyz="0 0 0"
        rpy="0 0 0" />
      <geometry>
        <mesh
          filename="package://robot_controller/description/meshes_arms_gen3/AL4.STL" />
      </geometry>
    </collision>
  </link>
  <joint
    name="AJL4"
    type="revolute">
    <origin
      xyz="-0.0424 0 -0.186"
      rpy="1.5708 0 1.5708" />
    <parent
      link="AL3" />
    <child
      link="AL4" />
    <axis
      xyz="0 0 1" />
    <limit lower="-0.0174532925199433" upper="2.00712864" effort="100" velocity="1" />
  </joint>
  <link
    name="AL5">
    <inertial>
      <origin
        xyz="1.803E-05 0.0011913 -0.034704"
        rpy="0 0 0" />
      <mass
        value="0.99941" />
      <inertia
        ixx="0.00046967"
        ixy="5.4165E-07"
        ixz="-5.0766E-07"
        iyy="0.00038226"
        iyz="1.8522E-06"
        izz="0.00046884" />
    </inertial>
    <visual>
      <origin
        xyz="0 0 0"
        rpy="0 0 0" />
      <geometry>
        <mesh
          filename="package://robot_controller/description/meshes_arms_gen3/AL5.STL" />
      </geometry>
      <material
        name="">
        <color
          rgba="0.89804 0.91765 0.92941 1" />
      </material>
    </visual>
    <collision>
      <origin
        xyz="0 0 0"
        rpy="0 0 0" />
      <geometry>
        <mesh
          filename="package://robot_controller/description/meshes_arms_gen3/AL5.STL" />
      </geometry>
    </collision>
  </link>
  <joint
    name="AJL5"
    type="revolute">
    <origin
      xyz="0 -0.14 0.0424"
      rpy="-1.5708 0 0" />
    <parent
      link="AL4" />
    <child
      link="AL5" />
    <axis
      xyz="0 0 1" />
    <limit lower="-3.14" upper="3.14" effort="100" velocity="1" />
  </joint>
  <link
    name="AL6">
    <inertial>
      <origin
        xyz="0.0013138 -0.067927 0.033214"
        rpy="0 0 0" />
      <mass
        value="1.0311" />
      <inertia
        ixx="0.00038835"
        ixy="1.5406E-06"
        ixz="7.268E-09"
        iyy="0.00046996"
        iyz="8.5446E-06"
        izz="0.00048276" />
    </inertial>
    <visual>
      <origin
        xyz="0 0 0"
        rpy="0 0 0" />
      <geometry>
        <mesh
          filename="package://robot_controller/description/meshes_arms_gen3/AL6.STL" />
      </geometry>
      <material
        name="">
        <color
          rgba="0.89804 0.91765 0.92941 1" />
      </material>
    </visual>
    <collision>
      <origin
        xyz="0 0 0"
        rpy="0 0 0" />
      <geometry>
        <mesh
          filename="package://robot_controller/description/meshes_arms_gen3/AL6.STL" />
      </geometry>
    </collision>
  </link>
  <joint
    name="AJL6"
    type="revolute">
    <origin
      xyz="0 0.0345 -0.035"
      rpy="1.5708 0 0" />
    <parent
      link="AL5" />
    <child
      link="AL6" />
    <axis
      xyz="0 0 1" />
    <limit lower="-1.5707963267949" upper="1.5707963267949" effort="100" velocity="1" />
  </joint>

  <link name="l_hand_base_Link">
    <inertial>
      <origin
        xyz="0.068045 -0.00089833 -0.03139"
        rpy="0 0 0" />
      <mass
        value="0.16133" />
      <inertia
        ixx="9.2267E-05"
        ixy="-3.1538E-06"
        ixz="1.0576E-05"
        iyy="0.00014599"
        iyz="-1.4837E-06"
        izz="0.00010832" />
    </inertial>
    <visual>
      <origin
        xyz="0 0 0"
        rpy="3.1415926 0 0" />
      <geometry>
        <mesh
          filename="package://robot_controller//description/meshes_arms_gen2/L_hand_base_link.STL" />
      </geometry>
      <material
        name="">
        <color
          rgba="0.89804 0.91765 0.92941 1" />
      </material>
    </visual>
    <collision>
      <origin
        xyz="0 0 0"
        rpy="0 0 0" />
      <geometry>
        <mesh
          filename="package://robot_controller//description/meshes_arms_gen2/L_hand_base_link.STL" />
      </geometry>
    </collision>
  </link>

  <joint
    name="AJL7"
    type="revolute">
    <origin
      xyz="0.0345 -0.07 0.0345"
      rpy="1.5708 0 -1.5708" />
    <parent
      link="AL6" />
    <child
      link="l_hand_base_Link" />
    <axis
      xyz="0 0 1" /> 
    <limit lower="-1.5707963267949" upper="1.5707963267949" effort="100" velocity="1" />
  </joint>

  <link name="l_thumb_proximal_base">
    <inertial>
      <origin
        xyz="0.004803 -0.00095531 -0.00757"
        rpy="0 0 0" />
      <mass
        value="0.0018869" />
      <inertia
        ixx="5.0976E-08"
        ixy="-2.9825E-09"
        ixz="-4.8658E-09"
        iyy="8.6346E-08"
        iyz="-2.9039E-11"
        izz="6.7434E-08" />
    </inertial>
    <visual>
      <origin
        xyz="0 0 0"
        rpy="0 0 0" />
      <geometry>
        <mesh
          filename="package://robot_controller//description/meshes_arms_gen2/L_thumb_proximal_base.STL" />
      </geometry>
      <material
        name="">
        <color
          rgba="0.89804 0.91765 0.92941 1" />
      </material>
    </visual>
    <collision>
      <origin
        xyz="0 0 0"
        rpy="0 0 0" />
      <geometry>
        <mesh
          filename="package://robot_controller//description/meshes_arms_gen2/L_thumb_proximal_base.STL" />
      </geometry>
    </collision>
  </link>
  <joint name="l_hand_Joint1" type="revolute">
    <origin
      xyz="0.0844 0.01496 0.01705"
      rpy="0 1.5708 0" />
    <parent
      link="l_hand_base_Link" />
    <child
      link="l_thumb_proximal_base" />
    <axis
      xyz="0 0 1" />
    <limit
      lower="-0.1"
      upper="1.3"
      effort="1"
      velocity="0.5" />
  </joint>
  <link name="l_thumb_proximal">
    <inertial>
      <origin
        xyz="0.02195 -0.01276 -0.0080375"
        rpy="0 0 0" />
      <mass
        value="0.0066087" />
      <inertia
        ixx="1.567E-06"
        ixy="7.8293E-07"
        ixz="7.8804E-10"
        iyy="1.7374E-06"
        iyz="1.1362E-09"
        izz="2.7864E-06" />
    </inertial>
    <visual>
      <origin
        xyz="0 0 0"
        rpy="0 0 0" />
      <geometry>
        <mesh
          filename="package://robot_controller//description/meshes_arms_gen2/L_thumb_proximal.STL" />
      </geometry>
      <material
        name="">
        <color
          rgba="0.89804 0.91765 0.92941 1" />
      </material>
    </visual>
    <collision>
      <origin
        xyz="0 0 0"
        rpy="0 0 0" />
      <geometry>
        <mesh
          filename="package://robot_controller//description/meshes_arms_gen2/L_thumb_proximal.STL" />
      </geometry>
    </collision>
  </link>
  <joint name="l_hand_Joint2" type="revolute">
    <origin
      xyz="0.012283 0.0067357 -0.00925"
      rpy="-1.5708 0 -0.10622" />
    <parent
      link="l_thumb_proximal_base" />
    <child
      link="l_thumb_proximal" />
    <axis
      xyz="0 0 -1" />
    <limit
      lower="-0.1"
      upper="0.6"
      effort="1"
      velocity="0.5" />
  </joint>
  <link name="l_thumb_intermediate">
    <inertial>
      <origin
        xyz="0.0093024 -0.0027183 0.0072004"
        rpy="0 0 0" />
      <mass
        value="0.0037848" />
      <inertia
        ixx="3.4664E-07"
        ixy="-1.0122E-07"
        ixz="-5.013E-12"
        iyy="3.4715E-07"
        iyz="3.1367E-12"
        izz="4.6534E-07" />
    </inertial>
    <visual>
      <origin
        xyz="0 0 0"
        rpy="0 0 0" />
      <geometry>
        <mesh
          filename="package://robot_controller//description/meshes_arms_gen2/L_thumb_intermediate.STL" />
      </geometry>
      <material
        name="">
        <color
          rgba="0.89804 0.91765 0.92941 1" />
      </material>
    </visual>
    <collision>
      <origin
        xyz="0 0 0"
        rpy="0 0 0" />
      <geometry>
        <mesh
          filename="package://robot_controller//description/meshes_arms_gen2/L_thumb_intermediate.STL" />
      </geometry>
    </collision>
  </link>
  <joint name="l_hand_Joint2_mimic1" type="revolute">
    <origin
      xyz="0.044114 -0.034497 -0.0008"
      rpy="3.1416 0 0" />
    <parent
      link="l_thumb_proximal" />
    <child
      link="l_thumb_intermediate" />
    <axis
      xyz="0 0 1" />
    <limit
      lower="0"
      upper="0.8"
      effort="1"
      velocity="0.5" />
    <mimic
      joint="l_hand_Joint2"
      multiplier="1.6"
      offset="0" />
  </joint>
  <link name="l_thumb_distal">
    <inertial>
      <origin
        xyz="0.0096049 0.0043079 0.0060034"
        rpy="0 0 0" />
      <mass
        value="0.0033441" />
      <inertia
        ixx="1.2862E-07"
        ixy="-5.5973E-08"
        ixz="9.9733E-11"
        iyy="1.4823E-07"
        iyz="1.1471E-10"
        izz="2.0026E-07" />
    </inertial>
    <visual>
      <origin
        xyz="0 0 0"
        rpy="0 0 0" />
      <geometry>
        <mesh
          filename="package://robot_controller//description/meshes_arms_gen2/L_thumb_distal.STL" />
      </geometry>
      <material
        name="">
        <color
          rgba="0.89804 0.91765 0.92941 1" />
      </material>
    </visual>
    <collision>
      <origin
        xyz="0 0 0"
        rpy="0 0 0" />
      <geometry>
        <mesh
          filename="package://robot_controller//description/meshes_arms_gen2/L_thumb_distal.STL" />
      </geometry>
    </collision>
  </link>
  <joint name="l_hand_Joint2_mimic2" type="revolute">
    <origin
      xyz="0.021283 0.0077561 0.0012"
      rpy="0 0 0" />
    <parent
      link="l_thumb_intermediate" />
    <child
      link="l_thumb_distal" />
    <axis
      xyz="0 0 1" />
    <limit
      lower="0"
      upper="1.2"
      effort="1"
      velocity="0.5" />
    <mimic
      joint="l_hand_Joint2"
      multiplier="2.4"
      offset="0" />
  </joint>
  <link name="l_thumb_tip" />
  <joint name="l_set_thumb_tip" type="fixed">
      <parent link="l_thumb_distal"/>
      <child link="l_thumb_tip"/>
      <origin 
      xyz="0.01 0.013 0.006" 
      rpy="0 0 0.47" />
  </joint>
  <link name="l_index_proximal">
    <inertial>
      <origin
        xyz="0.011988 0.00061832 -0.0060001"
        rpy="0 0 0" />
      <mass
        value="0.0042404" />
      <inertia
        ixx="2.1103E-07"
        ixy="7.1453E-09"
        ixz="-6.1515E-12"
        iyy="6.6276E-07"
        iyz="6.568E-12"
        izz="6.9398E-07" />
    </inertial>
    <visual>
      <origin
        xyz="0 0 0"
        rpy="0 0 0" />
      <geometry>
        <mesh
          filename="package://robot_controller//description/meshes_arms_gen2/L_index_proximal.STL" />
      </geometry>
      <material
        name="">
        <color
          rgba="0.89804 0.91765 0.92941 1" />
      </material>
    </visual>
    <collision>
      <origin
        xyz="0 0 0"
        rpy="0 0 0" />
      <geometry>
        <mesh
          filename="package://robot_controller//description/meshes_arms_gen2/L_index_proximal.STL" />
      </geometry>
    </collision>
  </link>
  <joint name="l_hand_Joint3" type="revolute">
    <origin
      xyz="0.15183 -0.0022853 0.0052323"
      rpy="3.1415926 0.034907 0" />
    <parent
      link="l_hand_base_Link" />
    <child
      link="l_index_proximal" />
    <axis
      xyz="0 0 -1" />
    <limit
      lower="0"
      upper="1.7"
      effort="1"
      velocity="0.5" />
  </joint>
  <link name="l_index_distal">
    <inertial>
      <origin
        xyz="0.01958 0.0020638 -0.005"
        rpy="0 0 0" />
      <mass
        value="0.0045683" />
      <inertia
        ixx="9.5249E-08"
        ixy="8.444E-08"
        ixz="-2.4041E-12"
        iyy="7.6194E-07"
        iyz="8.3381E-13"
        izz="7.8179E-07" />
    </inertial>
    <visual>
      <origin
        xyz="0 0 0"
        rpy="0 0 0" />
      <geometry>
        <mesh
          filename="package://robot_controller//description/meshes_arms_gen2/L_index_distal.STL" />
      </geometry>
      <material
        name="">
        <color
          rgba="0.89804 0.91765 0.92941 1" />
      </material>
    </visual>
    <collision>
      <origin
        xyz="0 0 0"
        rpy="0 0 0" />
      <geometry>
        <mesh
          filename="package://robot_controller//description/meshes_arms_gen2/L_index_distal.STL" />
      </geometry>
    </collision>
  </link>
  <joint name="l_hand_Joint3_mimic" type="revolute">
    <origin
      xyz="0.031852 -0.0042351 -0.001"
      rpy="0 0 0" />
    <parent
      link="l_index_proximal" />
    <child
      link="l_index_distal" />
    <axis
      xyz="0 0 -1" />
    <limit
      lower="0"
      upper="1.7"
      effort="1"
      velocity="0.5" />
    <mimic
      joint="l_hand_Joint3"
      multiplier="1"
      offset="0" />
  </joint>
  <link name="l_index_tip" />
  <joint name="l_set_index_tip" type="fixed">
      <parent link="l_index_distal"/>
      <child link="l_index_tip"/>
      <origin 
      xyz="0.042 -0.005 -0.005" 
      rpy="0 0 0" />
  </joint>

  <link name="l_middle_proximal">
    <inertial>
      <origin
        xyz="0.011934 0.001297 -0.0060001"
        rpy="0 0 0" />
      <mass
        value="0.0042402" />
      <inertia
        ixx="2.1167E-07"
        ixy="-1.8461E-08"
        ixz="-6.4243E-12"
        iyy="6.621E-07"
        iyz="5.6821E-12"
        izz="6.9396E-07" />
    </inertial>
    <visual>
      <origin
        xyz="0 0 0"
        rpy="0 0 0" />
      <geometry>
        <mesh
          filename="package://robot_controller//description/meshes_arms_gen2/L_middle_proximal.STL" />
      </geometry>
      <material
        name="">
        <color
          rgba="0.89804 0.91765 0.92941 1" />
      </material>
    </visual>
    <collision>
      <origin
        xyz="0 0 0"
        rpy="0 0 0" />
      <geometry>
        <mesh
          filename="package://robot_controller//description/meshes_arms_gen2/L_middle_proximal.STL" />
      </geometry>
    </collision>
  </link>
  <joint name="l_hand_Joint4" type="revolute">
    <origin
      xyz="0.1524 -0.0022853 0.02455"
      rpy="3.1415926 0 0" />
    <parent
      link="l_hand_base_Link" />
    <child
      link="l_middle_proximal" />
    <axis
      xyz="0 0 -1" />
    <limit
      lower="0"
      upper="1.7"
      effort="1"
      velocity="0.5" />
  </joint>
  <link name="l_middle_distal">
    <inertial>
      <origin
        xyz="0.020796 0.001921 -0.0049999"
        rpy="0 0 0" />
      <mass
        value="0.0050397" />
      <inertia
        ixx="1.0646E-07"
        ixy="1.1425E-07"
        ixz="-3.7222E-12"
        iyy="9.5823E-07"
        iyz="-2.416E-12"
        izz="9.8384E-07" />
    </inertial>
    <visual>
      <origin
        xyz="0 0 0"
        rpy="0 0 0" />
      <geometry>
        <mesh
          filename="package://robot_controller//description/meshes_arms_gen2/L_middle_distal.STL" />
      </geometry>
      <material
        name="">
        <color
          rgba="0.89804 0.91765 0.92941 1" />
      </material>
    </visual>
    <collision>
      <origin
        xyz="0 0 0"
        rpy="0 0 0" />
      <geometry>
        <mesh
          filename="package://robot_controller//description/meshes_arms_gen2/L_middle_distal.STL" />
      </geometry>
    </collision>
  </link>
  <joint name="l_hand_Joint4_mimic" type="revolute">
    <origin
      xyz="0.032041 -0.0024229 -0.001"
      rpy="0 0 0" />
    <parent
      link="l_middle_proximal" />
    <child
      link="l_middle_distal" />
    <axis
      xyz="0 0 -1" />
    <limit
      lower="0"
      upper="1.7"
      effort="1"
      velocity="0.5" />
    <mimic
      joint="l_hand_Joint4"
      multiplier="1"
      offset="0" />
  </joint>
  <link name="l_ring_proximal">
    <inertial>
      <origin
        xyz="-0.011934 -0.001297 -0.0060002"
        rpy="0 0 0" />
      <mass
        value="0.0042403" />
      <inertia
        ixx="2.1167E-07"
        ixy="-1.8462E-08"
        ixz="7.9091E-12"
        iyy="6.621E-07"
        iyz="-5.3181E-12"
        izz="6.9396E-07" />
    </inertial>
    <visual>
      <origin
        xyz="0 0 0"
        rpy="0 0 0" />
      <geometry>
        <mesh
          filename="package://robot_controller//description/meshes_arms_gen2/L_ring_proximal.STL" />
      </geometry>
      <material
        name="">
        <color
          rgba="0.89804 0.91765 0.92941 1" />
      </material>
    </visual>
    <collision>
      <origin
        xyz="0 0 0"
        rpy="0 0 0" />
      <geometry>
        <mesh
          filename="package://robot_controller//description/meshes_arms_gen2/L_ring_proximal.STL" />
      </geometry>
    </collision>
  </link>
  <joint name="l_hand_Joint5" type="revolute">
    <origin
      xyz="0.15221 -0.0022853 0.043787"
      rpy="3.1416 0.05236 3.1416" />
    <parent
      link="l_hand_base_Link" />
    <child
      link="l_ring_proximal" />
    <axis
      xyz="0 0 -1" />
    <limit
      lower="0"
      upper="1.7"
      effort="1"
      velocity="0.5" />
  </joint>
  <link name="l_ring_distal">
    <inertial>
      <origin
        xyz="-0.019568 -0.0021753 -0.005"
        rpy="0 0 0" />
      <mass
        value="0.0045683" />
      <inertia
        ixx="9.4309E-08"
        ixy="8.0639E-08"
        ixz="2.255E-12"
        iyy="7.6288E-07"
        iyz="-5.5384E-13"
        izz="7.818E-07" />
    </inertial>
    <visual>
      <origin
        xyz="0 0 0"
        rpy="0 0 0" />
      <geometry>
        <mesh
          filename="package://robot_controller//description/meshes_arms_gen2/L_ring_distal.STL" />
      </geometry>
      <material
        name="">
        <color
          rgba="0.89804 0.91765 0.92941 1" />
      </material>
    </visual>
    <collision>
      <origin
        xyz="0 0 0"
        rpy="0 0 0" />
      <geometry>
        <mesh
          filename="package://robot_controller//description/meshes_arms_gen2/L_ring_distal.STL" />
      </geometry>
    </collision>
  </link>
  <joint name="l_hand_Joint5_mimic" type="revolute">
    <origin
      xyz="-0.032041 0.0024229 -0.001"
      rpy="0 0 0" />
    <parent
      link="l_ring_proximal" />
    <child
      link="l_ring_distal" />
    <axis
      xyz="0 0 -1" />
    <limit
      lower="0"
      upper="1.7"
      effort="1"
      velocity="0.5" />
    <mimic
      joint="l_hand_Joint5"
      multiplier="1"
      offset="0" />
  </joint>
  <link name="l_pinky_prximal">
    <inertial>
      <origin
        xyz="0.011934 0.001297 -0.0060002"
        rpy="0 0 0" />
      <mass
        value="0.0042403" />
      <inertia
        ixx="2.1167E-07"
        ixy="-1.8461E-08"
        ixz="-8.0869E-12"
        iyy="6.621E-07"
        iyz="5.4936E-12"
        izz="6.9396E-07" />
    </inertial>
    <visual>
      <origin
        xyz="0 0 0"
        rpy="0 0 0" />
      <geometry>
        <mesh
          filename="package://robot_controller//description/meshes_arms_gen2/L_pinky_prximal.STL" />
      </geometry>
      <material
        name="">
        <color
          rgba="0.89804 0.91765 0.92941 1" />
      </material>
    </visual>
    <collision>
      <origin
        xyz="0 0 0"
        rpy="0 0 0" />
      <geometry>
        <mesh
          filename="package://robot_controller//description/meshes_arms_gen2/L_pinky_prximal.STL" />
      </geometry>
    </collision>
  </link>
  <joint name="l_hand_Joint6" type="revolute">
    <origin
      xyz="0.15101 -0.0022853 0.062988"
      rpy="3.1415926 -0.10472 0" />
    <parent
      link="l_hand_base_Link" />
    <child
      link="l_pinky_prximal" />
    <axis
      xyz="0 0 -1" />
    <limit
      lower="0"
      upper="1.7"
      effort="1"
      velocity="0.5" />
  </joint>
  <link name="l_pinky_distal">
    <inertial>
      <origin
        xyz="0.016206 0.0024762 -0.0049944"
        rpy="0 0 0" />
      <mass
        value="0.0036017" />
      <inertia
        ixx="7.0275E-08"
        ixy="4.1395E-08"
        ixz="-9.1586E-11"
        iyy="4.3921E-07"
        iyz="-5.9552E-11"
        izz="4.4876E-07" />
    </inertial>
    <visual>
      <origin
        xyz="0 0 0"
        rpy="0 0 0" />
      <geometry>
        <mesh
          filename="package://robot_controller//description/meshes_arms_gen2/L_pinky_distal.STL" />
      </geometry>
      <material
        name="">
        <color
          rgba="0.89804 0.91765 0.92941 1" />
      </material>
    </visual>
    <collision>
      <origin
        xyz="0 0 0"
        rpy="0 0 0" />
      <geometry>
        <mesh
          filename="package://robot_controller//description/meshes_arms_gen2/L_pinky_distal.STL" />
      </geometry>
    </collision>
  </link>
  <joint name="l_hand_Joint6_mimic" type="revolute">
    <origin
      xyz="0.0320409414985167 -0.00242293156329965 -0.00099999999999999"
      rpy="0 0 0" />
    <parent
      link="l_pinky_prximal" />
    <child
      link="l_pinky_distal" />
    <axis
      xyz="0 0 -1" />
    <limit
      lower="0"
      upper="1.7"
      effort="1"
      velocity="0.5" />
    <mimic
      joint="l_hand_Joint6"
      multiplier="1"
      offset="0" />
  </joint>
</robot>