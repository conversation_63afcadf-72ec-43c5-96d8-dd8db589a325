/*
异常检测相关
    - 自碰撞检测
*/

#include "robot_controller/Def_Class.h"

namespace ANOM_DETEC {
    KINEMATICS::SpatialTransform poseTrans;

    /**
     * @brief 自碰撞检测
     * @details 循环检测对应连杆是否碰撞
     */
    bool AnomalyDetection::isSelfCollision(Eigen::VectorXd theta_r, Eigen::VectorXd theta_l, std::string robot_type, DynamicArray<std::shared_ptr<coal::ConvexBase>> robotModel){
        
        std::shared_ptr<coal::ConvexBase> base_link = robotModel[0];
        DynamicArray<std::shared_ptr<coal::ConvexBase>> r_arm_link;
        DynamicArray<std::shared_ptr<coal::ConvexBase>> l_arm_link;

        for(int i=0; i<7; i++){
            r_arm_link.push_back(robotModel[i+1]);
            l_arm_link.push_back(robotModel[i+8]);
        }
        
        std::vector<Eigen::Vector3d> relative_eular_r;
        std::vector<Eigen::Vector3d> relative_position_r;
        std::vector<Eigen::Vector3d> relative_eular_l;
        std::vector<Eigen::Vector3d> relative_position_l;
        if(robot_type == "gen2"){
            relative_eular_r = {
                Eigen::Vector3d(0,0,0),
                Eigen::Vector3d(-M_PI/2,0,-M_PI/2),
                Eigen::Vector3d(M_PI/2,0,M_PI/2),
                Eigen::Vector3d(-M_PI/2,0,M_PI/2),
                Eigen::Vector3d(M_PI/2,0,0),
                Eigen::Vector3d(-M_PI/2,0,0),
                Eigen::Vector3d(-M_PI/2,0,-M_PI/2)
            };
            relative_position_r = {
                Eigen::Vector3d(0, 0, 0.2016),
                Eigen::Vector3d(0.0356, 0, 0.052),
                Eigen::Vector3d(0.049, 0, -0.035),
                Eigen::Vector3d(-0.041, 0, 0.1996),
                Eigen::Vector3d(0, -0.1395, -0.041),
                Eigen::Vector3d(0, 0.0295, 0.035),
                Eigen::Vector3d(0.0295, -0.07, -0.0295)
            };
            relative_eular_l = {
                Eigen::Vector3d(0, 0, 0),
                Eigen::Vector3d(M_PI / 2, 0, -M_PI / 2),
                Eigen::Vector3d(-M_PI / 2, 0, M_PI / 2),
                Eigen::Vector3d(M_PI / 2, 0, M_PI / 2),
                Eigen::Vector3d(-M_PI / 2, 0, 0),
                Eigen::Vector3d(M_PI / 2, 0, 0),
                Eigen::Vector3d(M_PI / 2, 0, -M_PI / 2)
            };
            relative_position_l = {
                Eigen::Vector3d(0, 0, -0.2066),
                Eigen::Vector3d(0.0416, 0, -0.047),
                Eigen::Vector3d(0.049, 0, 0.041),
                Eigen::Vector3d(-0.041, 0, -0.1996),
                Eigen::Vector3d(0, -0.1395, 0.041),
                Eigen::Vector3d(0, 0.0295, -0.035),
                Eigen::Vector3d(0.0295, -0.07, 0.0295)
            };
        }else{
            relative_eular_r = {
                Eigen::Vector3d(M_PI/2,0,0),
                Eigen::Vector3d(-M_PI/2,0,-M_PI/2),
                Eigen::Vector3d(M_PI/2,0,M_PI/2),
                Eigen::Vector3d(-M_PI/2,0,M_PI/2),
                Eigen::Vector3d(M_PI/2,0,0),
                Eigen::Vector3d(-M_PI/2,0,0),
                Eigen::Vector3d(-M_PI/2,0,-M_PI/2)
            };
            relative_position_r = {
                Eigen::Vector3d(0, -0.193, 0.055),
                Eigen::Vector3d(0.0565, 0, 0.067),
                Eigen::Vector3d(0.059, 0, -0.0565),
                Eigen::Vector3d(-0.0424, 0, 0.186),
                Eigen::Vector3d(0, -0.14, -0.0424),
                Eigen::Vector3d(0, 0.0345, 0.035),
                Eigen::Vector3d(0.0345, -0.07, -0.0345)
            };
            relative_eular_l = {
                Eigen::Vector3d(M_PI / 2, 0, 0),
                Eigen::Vector3d(M_PI / 2, 0, -M_PI / 2),
                Eigen::Vector3d(-M_PI / 2, 0, M_PI / 2),
                Eigen::Vector3d(M_PI / 2, 0, M_PI / 2),
                Eigen::Vector3d(-M_PI / 2, 0, 0),
                Eigen::Vector3d(M_PI / 2, 0, 0),
                Eigen::Vector3d(M_PI / 2, 0, -M_PI / 2)
            };
            relative_position_l = {
                Eigen::Vector3d(0, 0.193, 0.055),
                Eigen::Vector3d(0.0565, 0, -0.067),
                Eigen::Vector3d(0.059, 0, 0.0565),
                Eigen::Vector3d(-0.0424, 0, -0.186),
                Eigen::Vector3d(0, -0.14, 0.0424),
                Eigen::Vector3d(0, 0.0345, -0.035),
                Eigen::Vector3d(0.0345, -0.07, 0.0345)
            };
        }

        // Self-Collision Matrix
        Eigen::MatrixXd SCM = Eigen::MatrixXd::Zero(15,15);
        SCM(2,0) = 1;
        SCM(3,0) = 1; 
        SCM(4,0) = 1; 
        SCM(5,0) = 1; SCM(5,3) = 1;
        SCM(6,0) = 1; SCM(6,1) = 1; SCM(6,2) = 1; SCM(6,3) = 1; SCM(6,4) = 1;
        SCM(7,0) = 1; SCM(7,1) = 1; SCM(7,2) = 1; SCM(7,3) = 1; SCM(7,4) = 1; SCM(7,5) = 1;
        SCM(8,6) = 1; SCM(8,7) = 1;
        SCM(9,0) = 1; SCM(9,5) = 1; SCM(9,6) = 1; SCM(9,7) = 1;
        SCM(10,0) = 1; SCM(10,4) = 1; SCM(10,5) = 1; SCM(10,6) = 1; SCM(10,7) = 1;
        SCM(11,0) = 1; SCM(11,3) = 1; SCM(11,4) = 1; SCM(11,5) = 1; SCM(11,6) = 1; SCM(11,7) = 1;
        SCM(12,0) = 1; SCM(12,2) = 1; SCM(12,3) = 1; SCM(12,4) = 1; SCM(12,5) = 1; SCM(12,6) = 1; SCM(12,7) = 1; SCM(12,10) = 1;
        SCM(13,0) = 1; SCM(13,1) = 1; SCM(13,2) = 1; SCM(13,3) = 1; SCM(13,4) = 1; SCM(13,5) = 1; SCM(13,6) = 1; SCM(13,7) = 1; SCM(13,8) = 1; SCM(13,9) = 1; SCM(13,10) = 1; SCM(13,11) = 1;
        SCM(14,0) = 1; SCM(14,1) = 1; SCM(14,2) = 1; SCM(14,3) = 1; SCM(14,4) = 1; SCM(14,5) = 1; SCM(14,6) = 1; SCM(14,7) = 1; SCM(14,8) = 1; SCM(14,9) = 1; SCM(14,10) = 1; SCM(14,11) = 1; SCM(14,12) = 1; 
    
        std::vector<bool> cd_res(15,false);

        coal::Transform3s T1;
        coal::Transform3s T2;
        std::shared_ptr<coal::ConvexBase> link1;
        std::shared_ptr<coal::ConvexBase> link2;
        std::string link_name1;
        std::string link_name2;

        Eigen::Matrix3d T0 = poseTrans.rot_x(M_PI/2);
        Eigen::Quaterniond Q0(T0);
        Q0.normalize();
        Eigen::VectorXd Qtd0(4);
        Qtd0 << Q0.coeffs();
        Eigen::Vector3d pos0 = Eigen::Vector3d(0, 0, 0.8);
        coal::Transform3s T_fcl0 = setPose_coal(Qtd0,pos0);

        DynamicArray<mesh_pose> arm_pose_r;
        DynamicArray<mesh_pose> arm_pose_l;
        arm_pose_r = get_arm_pose(relative_eular_r,relative_position_r,theta_r);
        arm_pose_l = get_arm_pose(relative_eular_l,relative_position_l,theta_l);

        coal::CollisionRequest request;
        coal::CollisionResult result;

        bool colli_flag = false;


        for(int i=0; i<15; i++){
            for(int j=0; j<15; j++){
                if(SCM(i,j)){
                    if(i == 0){
                        T1 = T_fcl0;
                        link1 = base_link;
                        link_name1 = "base_link";
                    }else if(i < 8){
                        T1 = arm_pose_r[i-1].T_fcl;
                        link1 = r_arm_link[i-1];
                        link_name1 = "right_link" + std::to_string(i);
                    }else{
                        T1 = arm_pose_l[i-8].T_fcl;
                        link1 = l_arm_link[i-8];
                        link_name1 = "left_link" + std::to_string(i-7);
                    }

                    if(j == 0){
                        T2 = T_fcl0;
                        link2 = base_link;
                        link_name2 = "base_link";
                    }else if(j < 8){
                        T2 = arm_pose_r[j-1].T_fcl;
                        link2 = r_arm_link[j-1];
                        link_name2 = "right_link" + std::to_string(j);
                    }else{
                        T2 = arm_pose_l[j-8].T_fcl;
                        link2 = l_arm_link[j-8];
                        link_name2 = "left_link" + std::to_string(j-7);
                    }

                    collide(link1.get(), T1, link2.get(), T2, request, result);
                    if(result.isCollision()){
                        cd_res[i] = true;
                        cd_res[j] = true;
                        ROS_WARN("Collision detected! ---- %s & %s",link_name1.c_str(),link_name2.c_str());
                        colli_flag = true;
                    }
                    result.clear();
                }
            }
        }
        return colli_flag;
    }
}