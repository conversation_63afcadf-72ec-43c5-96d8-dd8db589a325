/*
异常检测相关
    - 位置超限检测
    - 跳变检测
*/

#include "robot_controller/Def_Class.h"

namespace ANOM_DETEC {
    /**
     * @brief 位置超限检测
     * @details 设置位置限制，检查各个关节角度是否超限
     */
    bool AnomalyDetection::isExceedPosLimit(Eigen::VectorXd& pos_r, Eigen::VectorXd& pos_l){
        bool flag = false;
        // 每个关节的角度限制（弧度）
        Eigen::MatrixXd theta_limit(7,2);
        theta_limit << -M_PI, M_PI,
                       -M_PI*135/180, M_PI*5/180,
                       -M_PI*91/180, M_PI*91/180,
                       -M_PI*1/180, M_PI*115/180,
                       -M_PI, M_PI,
                       -M_PI/2, M_PI/2,
                       -M_PI/2, M_PI/2;

        auto posLimit = [&](Eigen::VectorXd& pos) {
            for (int i = 0; i < 7; i++) {
                if(pos(i) < theta_limit(i,0)){
                    pos(i) = theta_limit(i,0);
                }
                if(pos(i) > theta_limit(i,1)){
                    pos(i) = theta_limit(i,1);
                }
            }
        };
        // 检查左右手臂的关节位置
        // flag = checkLimit(pos_r, "right") || checkLimit(pos_l, "left");
        // return flag;
        posLimit(pos_l);
        posLimit(pos_r);

        return false;
    }

    /**
     * @brief 跳变检测
     * @details 检测关节速度是否是个很大的值（1000）
     */
    bool AnomalyDetection::isExceedVelLimit(const Eigen::VectorXd& vel_r, const Eigen::VectorXd& vel_l, double limit) {
        bool flag = false;
        // 辅助函数，用来检查关节速度是否超出限制
        auto checkVel = [&](const Eigen::VectorXd& vel, const std::string& arm_name) {
            for (int i = 0; i < 7; i++) {
                if (std::abs(vel(i)) > limit) {
                    ROS_WARN("joint%d of the %s arm has undergone a mutation", i + 1, arm_name.c_str());
                    return true;
                }
            }
            return false;
        };
        // 检查左右手臂的关节速度
        flag = checkVel(vel_r, "right") || checkVel(vel_l, "left");
        return flag;
    }
}