/*
异常检测相关
*/

#include "robot_controller/Def_Class.h"

namespace ANOM_DETEC {

    bool AnomalyDetection::anomalyDetection(Plan_Res& jnts_r, Plan_Res& jnts_l, bool is_colli_det, std::string robot_type, DynamicArray<std::shared_ptr<coal::ConvexBase>> robotModel){
        bool mutation_flag = false;

        if(isExceedPosLimit(jnts_r.pos, jnts_l.pos) || isExceedVelLimit(jnts_r.vel, jnts_l.vel)){
            mutation_flag = true;
        }
        if(is_colli_det){
            if(isSelfCollision(jnts_r.pos, jnts_l.pos, robot_type, robotModel)){
                mutation_flag = true;
            }
        }

        return mutation_flag;
    }
}