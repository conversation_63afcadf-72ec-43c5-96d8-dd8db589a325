/*
轨迹规划-动态运动基元
*/

#include "robot_controller/Def_Class.h"

namespace TRAJ_PLAN{
    /**
     * @brief 读取指定目录下的文件
     * @details 获取示教数据（关节角度序列和臂角序列）
     */
    Demon_Traj DMP::dataExt_(std::string pathName1, std::string pathName2){
        auto readFileToData = [](const std::string& path) -> std::vector<std::vector<double>> {
            std::ifstream infile(path);
            if (!infile.is_open()) {
                throw std::runtime_error("Failed to open file: " + path);
            }

            std::vector<std::vector<double>> data;
            std::string s;
            while (getline(infile, s)) {
                std::istringstream is(s);
                std::vector<double> line;
                double d;
                while (is >> d) {
                    line.push_back(d);
                }
                data.push_back(line);
            }
            infile.close();
            return data;
        };

        std::vector<std::vector<double>> data1 = readFileToData(pathName1);
        std::vector<std::vector<double>> data2 = readFileToData(pathName2);

        int len = data1.size();
        Demon_Traj origin_data;

        origin_data.time.resize(len);
        origin_data.pos_ta1.resize(len);
        origin_data.pos_ta2.resize(len);
        origin_data.pos_ta3.resize(len);
        origin_data.pos_ta4.resize(len);
        origin_data.vel_ta1.resize(len);
        origin_data.vel_ta2.resize(len);
        origin_data.vel_ta3.resize(len);
        origin_data.vel_ta4.resize(len);
        origin_data.acc_ta1.resize(len);
        origin_data.acc_ta2.resize(len);
        origin_data.acc_ta3.resize(len);
        origin_data.acc_ta4.resize(len);
        origin_data.ArmJnt.resize(len);

        double dt;
        for (int i = 0; i < len; i++) {
            origin_data.time(i) = data1[i][0];
            origin_data.pos_ta1(i) = data1[i][1];
            origin_data.pos_ta2(i) = data1[i][2];
            origin_data.pos_ta3(i) = data1[i][3];
            origin_data.pos_ta4(i) = data1[i][4];
            origin_data.ArmJnt(i) = data2[i][1];
            if (i == 0) {
                origin_data.vel_ta1(i) = 0;
                origin_data.vel_ta2(i) = 0;
                origin_data.vel_ta3(i) = 0;
                origin_data.vel_ta4(i) = 0;
                origin_data.acc_ta1(i) = 0;
                origin_data.acc_ta2(i) = 0;
                origin_data.acc_ta3(i) = 0;
                origin_data.acc_ta4(i) = 0;
            }
            else {
                int j = i - 1;
                dt = origin_data.time(i) - origin_data.time(j);
                origin_data.vel_ta1(i) = (origin_data.pos_ta1(i) - origin_data.pos_ta1(j)) / dt;
                origin_data.vel_ta2(i) = (origin_data.pos_ta2(i) - origin_data.pos_ta2(j)) / dt;
                origin_data.vel_ta3(i) = (origin_data.pos_ta3(i) - origin_data.pos_ta3(j)) / dt;
                origin_data.vel_ta4(i) = (origin_data.pos_ta4(i) - origin_data.pos_ta4(j)) / dt;
                origin_data.acc_ta1(i) = (origin_data.vel_ta1(i) - origin_data.vel_ta1(j)) / dt;
                origin_data.acc_ta2(i) = (origin_data.vel_ta2(i) - origin_data.vel_ta2(j)) / dt;
                origin_data.acc_ta3(i) = (origin_data.vel_ta3(i) - origin_data.vel_ta3(j)) / dt;
                origin_data.acc_ta4(i) = (origin_data.vel_ta4(i) - origin_data.vel_ta4(j)) / dt;
                
            }
        }
        origin_data.ArmJnt = origin_data.ArmJnt.array() - origin_data.ArmJnt(0);
        return origin_data;
    }
    
    /**
     * @brief DMP训练
     */
    Train_Res DMP::dmpTrain_(Demon_Traj data, Train_Par par) {
        #pragma region INPUT&INIT
            Eigen::VectorXd time = data.time;
            int n = time.size();
            double t_end = time(n-1);
            // cout << t_end << endl;

            Eigen::MatrixXd x_demon(n, 4);
            x_demon.col(0) = data.pos_ta1;
            x_demon.col(1) = data.pos_ta2;
            x_demon.col(2) = data.pos_ta3;
            x_demon.col(3) = data.pos_ta4;

            Eigen::MatrixXd dx_demon(n, 4);
            dx_demon.col(0) = data.vel_ta1;
            dx_demon.col(1) = data.vel_ta2;
            dx_demon.col(2) = data.vel_ta3;
            dx_demon.col(3) = data.vel_ta4;

            Eigen::MatrixXd ddx_demon(n, 4);
            ddx_demon.col(0) = data.acc_ta1;
            ddx_demon.col(1) = data.acc_ta2;
            ddx_demon.col(2) = data.acc_ta3;
            ddx_demon.col(3) = data.acc_ta4;

            Eigen::Vector4d x_init = x_demon.row(0);
            Eigen::Vector4d x_goal = x_demon.row(n-1);

            double k = par.k;
            double alpha_y = par.alpha_y;
            int k_f = par.k_f;
            int N = par.nbfs;

            double tau = 1.0;
            double alpha_x = k * tau / t_end;
            double beta_y = alpha_y / 4;

            Eigen::VectorXd tmp = Eigen::VectorXd::Zero(N);
            Eigen::VectorXd c = Eigen::VectorXd::Zero(N);
            Eigen::VectorXd h = Eigen::VectorXd::Zero(N);
            Eigen::MatrixXd w = Eigen::MatrixXd::Ones(N,4);

            Eigen::VectorXd x = Eigen::VectorXd::Ones(n);
            Eigen::MatrixXd s = Eigen::MatrixXd::Zero(n,4);
            Eigen::MatrixXd ft = Eigen::MatrixXd::Zero(n,4);

            Eigen::MatrixXd psi = Eigen::MatrixXd::Ones(N,n);
        #pragma endregion
        
        #pragma region UPDATE
            tmp = Eigen::VectorXd::LinSpaced(N, 0, t_end);
            c = (-alpha_x / tau * tmp).array().exp();
            h = N / c.array().square();

            x = (-alpha_x / tau * time).array().exp();
            s = x.array() * (x_goal - x_init).array().abs();

            for (int i = 0; i < n; i++){
                ft.row(i) = tau * tau * ddx_demon.row(i) - alpha_y * (beta_y * (x_goal.transpose() - x_demon.row(i)) - tau * dx_demon.row(i)); 
            }

            for (int i = 0; i < N; i++)
            {
                for (int j = 0; j < n; j++)
                {
                    psi(i,j) = exp(-h(i) * pow(x(j)-c(i),2));
                }
            }

            Eigen::MatrixXd psi_x;
            double a,b;
            for (int i = 0; i < N; i++)
            {
                psi_x = psi.row(i).asDiagonal();
                for(int j = 0; j < 4; j++){
                    a = s.col(j).transpose() * psi_x * ft.col(j);
                    b = s.col(j).transpose() * psi_x * s.col(j);
                    w(i,j) = a / (b + 1.0e-15);
                }
                
            }
        #pragma endregion

        #pragma region OUTPUT
            Train_Res res;
            res.w = w;
            res.c = c;
            res.h = h;
            res.N = N;
            res.alpha_x = alpha_x;
            res.alpha_y = alpha_y;
            res.beta_y = beta_y;
            res.t_end = t_end;
            res.k_f = k_f;

            return res;
        #pragma endregion
    }
    
    /**
     * @brief 线性补偿
     */
    Plan_Res_MulDOF DMP::lnrCmp_(Plan_Res_MulDOF traj, Eigen::Vector4d g) {
        Eigen::VectorXd t = traj.t;
        double dt = t(1) - t(0);
        int n = t.size();

        Eigen::Vector4d traj_end = traj.pos.row(n-1).transpose();
        Eigen::Vector4d err = g - traj_end;

        Eigen::MatrixXd e = Eigen::MatrixXd::Zero(n,4);
        Eigen::MatrixXd x = Eigen::MatrixXd::Zero(n,4);
        Eigen::MatrixXd dx = Eigen::MatrixXd::Zero(n,4);
        Eigen::MatrixXd ddx = Eigen::MatrixXd::Zero(n,4);

        // 计算误差并调整轨迹
        Eigen::VectorXd exp_factors = (-30 * t.array()).exp();  // 计算指数衰减因子
        e = err * exp_factors.transpose().replicate(n, 1);  // 矩阵广播操作
        x += e;  // 更新轨迹位置

        // 计算速度和加速度
        dx = (x.bottomRows(n-1) - x.topRows(n-1)) / dt;  // 速度 = (x[i+1] - x[i]) / dt
        ddx = (dx.bottomRows(n-1) - dx.topRows(n-1)) / dt;  // 加速度 = (dx[i+1] - dx[i]) / dt
        
        Plan_Res_MulDOF res;
        res.t = t;
        res.pos = x;
        res.vel = dx;
        res.acc = ddx;
        return res;
    }

    /**
     * @brief DMP回归
     */
    Plan_Res_MulDOF DMP::dmpRegress_(Train_Res trainRes, Regress_Par par){
        Eigen::Vector4d y0 = par.x_init;
        Eigen::Vector4d g = par.x_goal;
        double tau = par.tau;
        double dt = par.dt;
        Eigen::MatrixXd w = trainRes.w;
        Eigen::VectorXd c = trainRes.c;
        Eigen::VectorXd h = trainRes.h;
        int N = trainRes.N;
        int k_f = trainRes.k_f;
        double alpha_x = trainRes.alpha_x;
        double alpha_y = trainRes.alpha_y;
        double beta_y = trainRes.beta_y;
        double t_end = trainRes.t_end;

        t_end = tau * t_end;
        int n = floor(t_end / dt) + 1;
        // cout << n << endl;

        Eigen::VectorXd t = Eigen::VectorXd::Zero(n);
        Eigen::VectorXd x = Eigen::VectorXd::Zero(n);

        Eigen::MatrixXd y = Eigen::MatrixXd::Zero(n,4);
        Eigen::MatrixXd dy = Eigen::MatrixXd::Zero(n,4);
        Eigen::MatrixXd ddy = Eigen::MatrixXd::Zero(n,4);

        Eigen::MatrixXd f_num = Eigen::MatrixXd::Zero(n,4);
        Eigen::MatrixXd f_denom = Eigen::MatrixXd::Zero(n,4);
        Eigen::MatrixXd f = Eigen::MatrixXd::Zero(n,4);

        Eigen::MatrixXd psi = Eigen::MatrixXd::Ones(N,n);

        for (int i = 0; i < n; i++)
        {
            t(i) = i * dt;
            x(i) = exp(-alpha_x / tau * t(i));
        }

        y.row(0) = y0.transpose();

        for (int i = 1; i < n; i++)
        {
            for (int j = 0; j < N; j++)
            {
                psi(j,i) = exp(-h(j) * pow(x(i)-c(j),2));
                f_num.row(i) = f_num.row(i) + psi(j,i) * w.row(j);
                f_denom.row(i) = f_denom.row(i).array() + psi(j,i) + 1.0e-10;
            }
            f(i,0) = (f_num(i,0) * x(i) * abs(g(0) - y0(0))) / f_denom(i,0);
            f(i,1) = (f_num(i,1) * x(i) * abs(g(1) - y0(1))) / f_denom(i,1);
            f(i,2) = (f_num(i,2) * x(i) * abs(g(2) - y0(2))) / f_denom(i,2);
            f(i,3) = (f_num(i,3) * x(i) * abs(g(3) - y0(3))) / f_denom(i,3);
            ddy.row(i) = (alpha_y * (beta_y * (g.transpose() - y.row(i-1)) - tau * dy.row(i-1)) + f.row(i)).array() / pow(tau,2);
            dy.row(i) = dy.row(i-1) + ddy.row(i) * dt;
            y.row(i) = y.row(i-1) + dy.row(i) * dt;
        }
        Plan_Res_MulDOF res;
        res.t = t;
        res.pos = y;
        res.vel = dy;
        res.acc = ddy;

        return res;
    }

    /**
     * @brief 限幅
     */
    Plan_Res_MulDOF DMP::limitAmplitude_(Plan_Res_MulDOF traj, Eigen::Vector4d y0, Eigen::Vector4d g, Eigen::Vector4d jntLimit){
        Eigen::VectorXd t = traj.t;
        double dt = t(1) - t(0);
        int n = t.size();

        traj.pos = traj.pos.array()*180/M_PI;
        y0 = y0.array()*180/M_PI;
        g = g.array()*180/M_PI;
        Eigen::MatrixXd y(n,4);

        Eigen::VectorXd::Index maxRow, minRow;
        Eigen::Vector4d m, p;
        double k_err;

        for(int i = 0; i < 4; i++){
            if(jntLimit(i) <= 0){
                m(i) = traj.pos.col(i).minCoeff(&minRow);
                p(i) = minRow;
                if(m(i) < jntLimit(i) && g(i) > jntLimit(i)){
                    for(int j = 0; j < n; j++){
                        if(j <= p(i)){
                            k_err = (jntLimit(i) - y0(i))/(m(i) - y0(i));
                            y(j,i) = k_err * (traj.pos(j,i) - y0(i)) + y0(i);
                        }else{
                            k_err = (jntLimit(i) - g(i))/(m(i) - g(i));
                            y(j,i) = k_err * (traj.pos(j,i) - g(i)) + g(i);
                        }
                    }
                }else if(m(i) < jntLimit(i) && g(i) < jntLimit(i)){
                    for(int j = 0; j < n; j++){
                        if(j <= p(i)){
                            k_err = ((g(i) - 20) - y0(i))/(m(i) - y0(i));
                            y(j,i) = k_err * (traj.pos(j,i) - y0(i)) + y0(i);
                        }else{
                            k_err = ((g(i) - 20) - g(i))/(m(i) - g(i));
                            y(j,i) = k_err * (traj.pos(j,i) - g(i)) + g(i);
                        }
                    }
                }else{
                    for(int j = 0; j < n; j++){
                        y(j,i) = traj.pos(j,i);
                    }
                }
            }else{
                m(i) = traj.pos.col(i).maxCoeff(&maxRow);
                p(i) = maxRow;
                if(m(i) > jntLimit(i) && g(i) < jntLimit(i)){
                    for(int j = 0; j < n; j++){
                        if(j <= p(i)){
                            k_err = (jntLimit(i) - y0(i))/(m(i) - y0(i));
                            y(j,i) = k_err * (traj.pos(j,i) - y0(i)) + y0(i);
                        }else{
                            k_err = (jntLimit(i) - g(i))/(m(i) - g(i));
                            y(j,i) = k_err * (traj.pos(j,i) - g(i)) + g(i);
                        }
                    }
                }else if(m(i) > jntLimit(i) && g(i) > jntLimit(i)){
                    for(int j = 0; j < n; j++){
                        if(j <= p(i)){
                            k_err = ((g(i) - 20) - y0(i))/(m(i) - y0(i));
                            y(j,i) = k_err * (traj.pos(j,i) - y0(i)) + y0(i);
                        }else{
                            k_err = ((g(i) - 20) - g(i))/(m(i) - g(i));
                            y(j,i) = k_err * (traj.pos(j,i) - g(i)) + g(i);
                        }
                    }
                }else{
                    for(int j = 0; j < n; j++){
                        y(j,i) = traj.pos(j,i);
                    }
                }
            }
        }

        m(0) = y.col(0).minCoeff(&minRow);
        p(0) = minRow;
        for(int i = 0; i < n; i++){
            if(i <= p(0)){
                k_err = (-45 - y0(0))/(m(0) - y0(0));
                y(i,0) = k_err * (y(i,0) - y0(0)) + y0(0);
            }else{
                k_err = (-45 - g(0))/(m(0) - g(0));
                y(i,0) = k_err * (y(i,0) - g(0)) + g(0);
            }
        }

        y = y.array()/180*M_PI;
        Eigen::MatrixXd dy = Eigen::MatrixXd::Zero(n,4);
        Eigen::MatrixXd ddy = Eigen::MatrixXd::Zero(n,4);

        for (int i = 1; i < n; i++)
        {
            dy.row(i) = (y.row(i) - y.row(i-1)) / dt;
            ddy.row(i) = (dy.row(i) - dy.row(i-1)) / dt;
        }

        Plan_Res_MulDOF res;
        res.t = t;
        res.pos = y;
        res.vel = dy;
        res.acc = ddy;
        return res;
    }

    /**
     * @brief DMP算法主体
     */
    Plan_Res_MulDOF DMP::dmp_(std::string pathJntPos, std::string pathAngArm, Train_Par trainPar, Eigen::VectorXd theta_init, DmpTargetPoints target, Eigen::Vector4d jntLimit, bool ikine_info_show, bool plan_info_show){
        TRAJ_PLAN::Interpolation interPlan;
        KINEMATICS::Unit_Conv unitConv;
        KINEMATICS::KDL_Solver kdlSolver("gen2","right");

        Eigen::Vector3d x_goal;
        x_goal = Eigen::Map<Eigen::VectorXd,Eigen::Unaligned>(target.pos_p.data(),target.pos_p.size());
        Eigen::Vector3d pose;
        pose = Eigen::Map<Eigen::VectorXd,Eigen::Unaligned>(target.pose_p.data(),target.pose_p.size());
        double phi = target.armJ_p;
        double dt = target.dt;
        double tau = target.tau;

        Plan_Res_MulDOF res;

        Demon_Traj data;
        data = dataExt_(pathJntPos,pathAngArm);
    
        Train_Res trainRes;
        trainRes = dmpTrain_(data,trainPar);
        int n = data.ArmJnt.size();
        double phi_start = data.ArmJnt(0);
        double phi_end = data.ArmJnt(n-1);
        phi_end = phi;
        
        JntPos jnt_pos_end;
        jnt_pos_end = kdlSolver.ik_kdl(x_goal,pose,phi);
        if(!jnt_pos_end.solved_flag || jnt_pos_end.error_flag){
            ROS_WARN("The iterative method failed!\nsolve_flag[%d], error_flag[%d]",jnt_pos_end.solved_flag,jnt_pos_end.error_flag);

            ROS_ERROR("planning failed![dmp]:move to [%.4f,%.4f,%.4f],[%.2f,%.2f,%.2f]",
                                                x_goal(0),x_goal(1),x_goal(2),
                                                unitConv.rad2deg(pose(0)),unitConv.rad2deg(pose(1)),unitConv.rad2deg(pose(2)));
            ros::shutdown();
            exit(0);
        }
        

        Eigen::VectorXd theta_goal = jnt_pos_end.theta;
        KDL::Frame fk_res = kdlSolver.fk_kdl(theta_goal);

        Regress_Par regPar;
        regPar.dt = dt;
        regPar.tau = tau;
        regPar.x_goal = theta_goal.head(4);
        regPar.x_init = theta_init.head(4);
        // cout << theta_init << endl;
        // cout << regPar.x_init << endl;
        
        Plan_Res_MulDOF regRes;
        regRes = dmpRegress_(trainRes,regPar);
        regRes = limitAmplitude_(regRes,theta_init.head(4),theta_goal.head(4),jntLimit);
        regRes = lnrCmp_(regRes,theta_goal.head(4));
        int n1 = regRes.t.size();

        double t_end = regRes.t(n1-1);
        Eigen::VectorXd inter_t_seg(3);
        Eigen::VectorXd inter_jnt5_pos_seg(3);
        Eigen::VectorXd inter_jnt6_pos_seg(3);
        Eigen::VectorXd inter_jnt7_pos_seg(3);
        inter_t_seg << 0, t_end/2, t_end;
        inter_jnt5_pos_seg << theta_init(4), theta_init(4), theta_goal(4);
        inter_jnt6_pos_seg << theta_init(5), theta_init(5), theta_goal(5);
        inter_jnt7_pos_seg << theta_init(6), theta_init(6), theta_goal(6);
        Plan_Res inter_jnt5_traj = interPlan.quinitic_poly(inter_t_seg,inter_jnt5_pos_seg,dt);
        Plan_Res inter_jnt6_traj = interPlan.quinitic_poly(inter_t_seg,inter_jnt6_pos_seg,dt);
        Plan_Res inter_jnt7_traj = interPlan.quinitic_poly(inter_t_seg,inter_jnt7_pos_seg,dt);

        Eigen::MatrixXd res_pos(n1,7);
        Eigen::MatrixXd res_vel(n1,7);
        Eigen::MatrixXd res_acc(n1,7);
        for(int i = 0; i < 4; i++){
            res_pos.col(i) = regRes.pos.col(i);
            res_vel.col(i) = regRes.vel.col(i);
            res_acc.col(i) = regRes.acc.col(i);
        }
        res_pos.col(4) = inter_jnt5_traj.pos;
        res_pos.col(5) = inter_jnt6_traj.pos;
        res_pos.col(6) = inter_jnt7_traj.pos;
        res_vel.col(4) = inter_jnt5_traj.vel;
        res_vel.col(5) = inter_jnt6_traj.vel;
        res_vel.col(6) = inter_jnt7_traj.vel;
        res_acc.col(4) = inter_jnt5_traj.acc;
        res_acc.col(5) = inter_jnt6_traj.acc;
        res_acc.col(6) = inter_jnt7_traj.acc;

        if(ikine_info_show){
            std::cout << "res_ikine: " << theta_goal(0)*180/M_PI << ", " << theta_goal(1)*180/M_PI << ", " << theta_goal(2)*180/M_PI << ", " << theta_goal(3)*180/M_PI << ", " << theta_goal(4)*180/M_PI << ", " << theta_goal(5)*180/M_PI << ", " << theta_goal(6)*180/M_PI << std::endl;
            std::cout << "cart_position_set: " << "[" << x_goal(0) << ", " << x_goal(1) << ", " << x_goal(2) << "]" << std::endl;
            std::cout << "cart_position " << "[" << fk_res.p.x() << ", " << fk_res.p.y() << ", " << fk_res.p.z() << "]" << std::endl;
        }
        if(plan_info_show){
            std::cout << "jnt_position_start_set: " << theta_init(0)*180/M_PI << ", " << theta_init(1)*180/M_PI << ", " << theta_init(2)*180/M_PI << ", " << theta_init(3)*180/M_PI << ", " << theta_init(4)*180/M_PI << ", " << theta_init(5)*180/M_PI << ", " << theta_init(6)*180/M_PI << std::endl;
            std::cout << "jnt_position_end_set: " << theta_goal(0)*180/M_PI << ", " << theta_goal(1)*180/M_PI << ", " << theta_goal(2)*180/M_PI << ", " << theta_goal(3)*180/M_PI << ", " << theta_goal(4)*180/M_PI << ", " << theta_goal(5)*180/M_PI << ", " << theta_goal(6)*180/M_PI << std::endl;
            std::cout << "jnt_position_start: " << res_pos(0,0)*180/M_PI << ", " << res_pos(0,1)*180/M_PI << ", " << res_pos(0,2)*180/M_PI << ", " << res_pos(0,3)*180/M_PI << ", " << res_pos(0,4)*180/M_PI << ", " << res_pos(0,5) << ", " << res_pos(0,6) << std::endl; 
            std::cout << "jnt_position_end " << res_pos(n1-1,0)*180/M_PI << ", " << res_pos(n1-1,1)*180/M_PI << ", " << res_pos(n1-1,2)*180/M_PI << ", " << res_pos(n1-1,3)*180/M_PI << ", " << res_pos(n1-1,4)*180/M_PI << ", " << res_pos(n1-1,5)*180/M_PI << ", " << res_pos(n1-1,6)*180/M_PI << std::endl;
        }
        
        res.t = regRes.t;
        res.pos = res_pos;
        res.vel = res_vel;
        res.acc = res_acc;
        res.n = n1;
        return res;
    }

    /**
     * @brief 预设动作1（抬手）
     */
    Plan_Res_MulDOF DMP::action1_dmp(Eigen::VectorXd& jnt_end, DmpTargetPoints target, bool check_start_pos, bool ikine_info_show, bool plan_info_show){
        using KINEMATICS::Unit_Conv;
        Unit_Conv unitConv;
        
        std::string path1 = "./src/robot_controller/data/demon_traj/Motion1_JntPos_v2.txt";
        std::string path2 = "./src/robot_controller/data/demon_traj/Motion1_AngArm_v2.txt";

        Eigen::VectorXd theta_dmp_init(7);
        theta_dmp_init << 0, -M_PI*8/180, 0, 0, 0, 0, 0;

        if(check_start_pos){
            double norm = (jnt_end - theta_dmp_init).norm();
            if(norm > 0.012){
                ROS_ERROR("The current position is inconsistent with the starting point of the DMP trajectory\n"
                "set pos: [%.2f,%.2f,%.2f,%.2f,%.2f,%.2f,%.2f]\n"
                "cur pos: [%.2f,%.2f,%.2f,%.2f,%.2f,%.2f,%.2f]\n"
                "norm: %.5f",
                unitConv.rad2deg(theta_dmp_init(0)),unitConv.rad2deg(theta_dmp_init(1)),
                unitConv.rad2deg(theta_dmp_init(2)),unitConv.rad2deg(theta_dmp_init(3)),
                unitConv.rad2deg(theta_dmp_init(4)),unitConv.rad2deg(theta_dmp_init(5)),
                unitConv.rad2deg(theta_dmp_init(6)),unitConv.rad2deg(jnt_end(0)),
                unitConv.rad2deg(jnt_end(1)),unitConv.rad2deg(jnt_end(2)),
                unitConv.rad2deg(jnt_end(3)),unitConv.rad2deg(jnt_end(4)),
                unitConv.rad2deg(jnt_end(5)),unitConv.rad2deg(jnt_end(6)),
                norm);

                ros::shutdown();
                exit(0);
            }
        }

        Train_Par trainPar;
        trainPar.nbfs = 50;
        trainPar.k = 7;
        trainPar.alpha_y = 10;
        trainPar.k_f = 1;

        Eigen::Vector4d jntLimit;
        jntLimit << -45,
                    -35,
                    30,
                    111;
        
        Plan_Res_MulDOF res = dmp_(path1,path2,trainPar,theta_dmp_init,target,jntLimit,ikine_info_show,plan_info_show);
        
        jnt_end = res.pos.row(res.n-1);
        return res;
    }
}