/*
回调函数
*/

#include "robot_controller/Def_Class.h"

namespace DATA_PROC{
    /**
     * @brief subActPos_话题订阅对象的回调函数
     * @details 将接收到的关节数据赋给jnt_pos_actual_l和jnt_pos_actual_r，并检查关节位置是否超限制，保存位置数据
     */
    void Data_Sub::jointStateCallback(const sensor_msgs::JointState::ConstPtr &msg){
        ActPos_flag = true;

        size_t position_size = msg->position.size();
        if (position_size != 14) {
            ROS_ERROR("JointState message size mismatch. Expected 14 joints, got %zu.", position_size);
            ros::shutdown();
            exit(0);
        }

        // 使用局部变量缓存
        const std::vector<double>& positions = msg->position;

        for(int i = 0; i < 7; i++)
        {
            jnt_pos_actual_l(i) = positions[i];
            jnt_pos_actual_r(i) = positions[i+7];
            if(std::abs(jnt_pos_actual_l(i))>M_PI){
                ROS_WARN("Left arm joint %d out of limit: %f", i + 1, jnt_pos_actual_l(i));
                ros::shutdown();
                exit(0);
            }
            if(std::abs(jnt_pos_actual_r(i))>M_PI){
                ROS_WARN("Right arm joint %d out of limit: %f", i + 1, jnt_pos_actual_r(i));
                ros::shutdown();
                exit(0);
            }
        }
        fileWrite(fwSubPos_,jnt_pos_actual_r,jnt_pos_actual_l);
    }

    /**
     * @brief 获取当前关节角度
     */
    void Data_Sub::getJntPos(Eigen::VectorXd &jnt_pos_right, Eigen::VectorXd &jnt_pos_left){
        ROS_INFO("waiting for current position...");
        ActPos_flag = false;
        ros::Rate loop_rate(10);  // 10Hz的频率
        while(!ActPos_flag && ros::ok()){
            loop_rate.sleep();
        }
        jnt_pos_right = jnt_pos_actual_r;
        jnt_pos_left = jnt_pos_actual_l;
        printJointAngles("Right Arm", jnt_pos_right);
        printJointAngles("Left Arm", jnt_pos_left);
    }
}