/*
回调函数
*/

#include "robot_controller/Def_Class.h"

namespace DATA_PROC{
    /**
     * @brief subCVPos_话题订阅对象的回调函数
     * @details 将接收到的目标点数据放进容器
     */
    void Data_Sub::camera_arrayCallback_base(const std_msgs::Float64MultiArray::ConstPtr& msg)
    {
        subCV_flag = true;

        // 直接缓存数据并检查大小，避免不必要的内存增长
        size_t data_size = msg->data.size();
        if (data_size == 0) {
            ROS_WARN("Received empty camera data.");
            return;
        }

        for (double value : msg->data) {
            container.push_back(value);
        }
        // 对容器的大小进行限制
        const size_t max_container_size = 1000;  // 限制最大容器大小
        if (container.size() > max_container_size) {
            container.erase(container.begin(), container.begin() + 500);  // 只保留最新的数据
        }
    }

    /**
     * @brief 等待接收视觉数据
     */
    bool Data_Sub::waitForCVData(size_t expected_size) {
        ROS_INFO("Waiting for target position from CV...");
        subCV_flag = false;
        ros::Rate rate(10); // 控制等待循环频率，10Hz
        while (!subCV_flag && ros::ok()) {
            rate.sleep();     // 让出CPU资源，避免占用过高
        }
        return container.size() >= expected_size;
    }

    /**
     * @brief 提取目标数据并更新
     */
    void Data_Sub::extractTargetPosition(Eigen::Vector3d &target_pos, size_t start_index) {
        target_pos(0) = container[start_index];
        target_pos(1) = -container[start_index + 1];  // Y轴反向
        target_pos(2) = container[start_index + 2];
    }

    /**
     * @brief 获取目标数据
     * @details 处理目标位置函数（无物体类型）
     */
    void Data_Sub::getCVPos(Eigen::Vector3d &target_pos){
        spinner2_->start();
        if (waitForCVData(3)) {
            extractTargetPosition(target_pos, 0);
            container.erase(container.begin(), container.begin() + 3);
            ROS_INFO("target position: [%.4f, %.4f, %.4f]",target_pos(0),target_pos(1),target_pos(2));
        }
        spinner2_->stop();
    }
    /**
     * @brief 获取目标数据
     * @details 处理目标位置函数（带物体类型）
     */
    void Data_Sub::getCVPos(Eigen::Vector3d &target_pos, int &obj_type){
        spinner2_->start();
        if (waitForCVData(4)) {
            extractTargetPosition(target_pos, 0);
            obj_type = container[3];
            container.erase(container.begin(), container.begin() + 4);
            ROS_INFO("target[%d] position: [%.4f, %.4f, %.4f]",obj_type,target_pos(0),target_pos(1),target_pos(2));
        }
        spinner2_->stop();
    }
    /**
     * @brief 获取目标数据
     * @details 处理两个目标位置
     */
    void Data_Sub::getCVPos(Eigen::Vector3d &target_pos1, Eigen::Vector3d &target_pos2){
        spinner2_->start();
        if (waitForCVData(6)) {
            extractTargetPosition(target_pos1, 0);
            extractTargetPosition(target_pos2, 4);
            container.erase(container.begin(), container.begin() + 6);
            ROS_INFO("target position1: [%.4f, %.4f, %.4f]",target_pos1(0),target_pos1(1),target_pos1(2));
            ROS_INFO("target position2: [%.4f, %.4f, %.4f]",target_pos2(0),target_pos2(1),target_pos2(2));
        }
        spinner2_->stop();
    }
    /**
     * @brief 获取目标数据
     * @details 处理两个目标位置和物体类型
     */
    void Data_Sub::getCVPos(Eigen::Vector3d &target_pos1, int &obj_type1, Eigen::Vector3d &target_pos2, int &obj_type2){
        spinner2_->start();
        if (waitForCVData(8)) {
            extractTargetPosition(target_pos1, 0);
            obj_type1 = container[3];
            extractTargetPosition(target_pos2, 4);
            obj_type2 = container[7];
            container.erase(container.begin(), container.begin() + 8);
            ROS_INFO("target[%d] position1: [%.4f, %.4f, %.4f]",obj_type1,target_pos1(0),target_pos1(1),target_pos1(2));
            ROS_INFO("target[%d] position2: [%.4f, %.4f, %.4f]",obj_type2,target_pos2(0),target_pos2(1),target_pos2(2));
        }
        spinner2_->stop();
    }
}