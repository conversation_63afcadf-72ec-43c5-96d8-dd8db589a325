/*
数据保存
*/

#include "robot_controller/Def_Class.h"

namespace DATA_PROC{
    void fileWrite(FILE *fpWrite, Eigen::VectorXd vec_r, Eigen::VectorXd vec_l){
        fprintf(fpWrite,"%.4f %.4f %.4f %.4f %.4f %.4f %.4f %.4f %.4f %.4f %.4f %.4f %.4f %.4f\n",
                        vec_r(0)*180/M_PI,
                        vec_r(1)*180/M_PI,
                        vec_r(2)*180/M_PI,
                        vec_r(3)*180/M_PI,
                        vec_r(4)*180/M_PI,
                        vec_r(5)*180/M_PI,
                        vec_r(6)*180/M_PI,
                        vec_l(0)*180/M_PI,
                        vec_l(1)*180/M_PI,
                        vec_l(2)*180/M_PI,
                        vec_l(3)*180/M_PI,
                        vec_l(4)*180/M_PI,
                        vec_l(5)*180/M_PI,
                        vec_l(6)*180/M_PI);
    }
}
