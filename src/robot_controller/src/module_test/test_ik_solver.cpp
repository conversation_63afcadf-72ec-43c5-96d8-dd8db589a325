#include "robot_controller/Def_Class.h"
#include "relaxed_ik_wrapper/relaxed_ik_wrapper.h"
#include <trac_ik/trac_ik.hpp>
KINEMATICS::SpatialTransform space_trans;

int main(int argc, char *argv[])
{
    setlocale(LC_ALL,"");
    ros::init(argc,argv,"test_ik_solver");
    ros::NodeHandle nh;

    // relaxed_ik_init
    std::string path_package = ros::package::getPath("robot_controller");

    std::string setting_file_path_arm_gen2_left = path_package + "/config/left_arm_settings.yaml";
    std::string setting_file_path_arm_gen2_right = path_package + "/config/right_arm_settings.yaml";

    std::string setting_file_path_arm_gen3_left = path_package + "/config/left_arm_gen3_settings.yaml";
    std::string setting_file_path_arm_gen3_right = path_package + "/config/right_arm_gen3_settings.yaml";
         
    RelaxedIKRust* relaxed_ik_arm2L = RelaxedIKRust::initialize(setting_file_path_arm_gen2_left);
    RelaxedIKRust* relaxed_ik_arm2R = RelaxedIKRust::initialize(setting_file_path_arm_gen2_right);
    RelaxedIKRust* relaxed_ik_arm3L = RelaxedIKRust::initialize(setting_file_path_arm_gen3_left);
    RelaxedIKRust* relaxed_ik_arm3R = RelaxedIKRust::initialize(setting_file_path_arm_gen3_right);

    std::vector<double> tolerances(6, 0.01);

    // KDL_ik_init
    KINEMATICS::KDL_Solver kdl_ik_arm2L("gen2","left");
    KINEMATICS::KDL_Solver kdl_ik_arm2R("gen2","right");
    KINEMATICS::KDL_Solver kdl_ik_arm3L("gen3","left");
    KINEMATICS::KDL_Solver kdl_ik_arm3R("gen3","right");

    // TEST1
    std::vector<double> positions_test1 = {0.41093, -0.32264, 1.3591};
    std::vector<double> orientations_test1 = {-0.62005, 0.041673, 0.27106, 0.73507};
    std::vector<double> qpositions_test1 = {0.990, -0.484, -0.794, 0.620, -0.478, 0.325, 0.325};

    Eigen::Map<Eigen::Vector3d> pos_test1(positions_test1.data());
    Eigen::Map<Eigen::Vector4d> quat_test1(orientations_test1.data());
    Eigen::Map<Eigen::VectorXd> qpos_test1(qpositions_test1.data(),qpositions_test1.size());
    Eigen::Vector3d eular_test1 = space_trans.quaternionToEulerAngles(quat_test1,false);

    KDL::Vector KDL_pos_test1(positions_test1[0],positions_test1[1],positions_test1[2]);
    KDL::Rotation rot = KDL::Rotation::RPY(eular_test1(2),eular_test1(1),eular_test1(0));
    KDL::Frame T_base_goal(rot, KDL_pos_test1);

    ROS_INFO_STREAM("\n" << BOLD << BLUE << "Test1: gen3[right]" << RESET << BLUE << "\n"
                    << "    position: ["  << pos_test1.transpose() << "]\n"
                    << "    orientation: quaternion[" << quat_test1.transpose() << "], eular: [" << eular_test1.transpose() << "]\n"
                    << "    joints: [" << qpos_test1.transpose() << "]\n" << RESET);
    
    ROS_INFO_STREAM(BOLD << GREEN << "relaxed_ik:" << RESET);
    std::vector<double> res_relaxed_ik;
    Eigen::VectorXd qpos_relaxed_ik;
    KDL::Frame fk_res;
    double err_pos;
    int cnt = 0;
    auto start = std::chrono::steady_clock::now();
    while(1){
        cnt++;
        res_relaxed_ik = relaxed_ik_arm3R->solve_position(positions_test1, orientations_test1, tolerances);
        qpos_relaxed_ik = Eigen::Map<Eigen::VectorXd>(res_relaxed_ik.data(),res_relaxed_ik.size());
        fk_res = kdl_ik_arm3R.fk_kdl(qpos_relaxed_ik);
        if((T_base_goal.p - fk_res.p).Norm() < 1e-2){
            KDL::Rotation R_error = T_base_goal.M.Inverse() * fk_res.M;

            KDL::Vector rot_axis;
            double angle_error_rad;
            R_error.GetRotAngle(rot_axis, angle_error_rad);

            double angle_error_deg = angle_error_rad * 180.0 / M_PI;

            ROS_INFO_STREAM(GREEN << "[Relaxed-ik Iter Count] " << cnt << RESET);
            ROS_INFO_STREAM(GREEN << "[Relaxed-ik Accuracy Test] Position error: " << (T_base_goal.p - fk_res.p).Norm() << " m" << RESET);
            ROS_INFO_STREAM(GREEN << "[Relaxed-ik Accuracy Test] Orientation error: " << angle_error_deg << " deg" << RESET);
            
            auto end = std::chrono::steady_clock::now();
            auto duration = std::chrono::duration_cast<std::chrono::microseconds>(end-start).count();
            ROS_INFO_STREAM(GREEN << "[Relaxed-ik Time Test] Cost " << (float)duration/1000000 <<"s");

            break;
        }
    }
    ROS_INFO_STREAM(GREEN << "joints: [" << qpos_relaxed_ik.transpose() << "]" << RESET);
    kdl_ik_arm3R.fk_kdl(qpos_relaxed_ik, true);
    ROS_INFO("\n");
    
    ROS_INFO_STREAM(BOLD << GREEN << "kdl_ik:" << RESET);
    pos_test1(2) -= 1.505;
    JntPos res_kdl_ik = kdl_ik_arm3R.ik_kdl(pos_test1, eular_test1, 0, true, true);
    if(!res_kdl_ik.error_flag){
        ROS_INFO_STREAM(GREEN << "joints: [" << res_kdl_ik.theta.transpose() << "]" << RESET);
        kdl_ik_arm3R.fk_kdl(res_kdl_ik.theta, true);
    }
    ROS_INFO("\n");

    
    return 0;
}