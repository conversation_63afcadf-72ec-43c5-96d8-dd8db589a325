#include <ros/ros.h>
#include <ros/package.h>
#include <kdl_parser/kdl_parser.hpp>
#include <trac_ik/trac_ik.hpp>
#include <urdf/model.h>
#include <yaml-cpp/yaml.h>

int main(int argc, char** argv)
{
    ros::init(argc, argv, "trac_ik_demo_node");
    ros::NodeHandle nh;

    // 加载配置文件
    std::string path_yaml = ros::package::getPath("robot_controller") + "/config/right_arm_gen3_settings_trac.yaml";
    YAML::Node config = YAML::LoadFile(path_yaml);

    std::string urdf_path = config["urdf_path"].as<std::string>();
    std::string base_link = config["base_link"].as<std::string>();
    std::string tip_link  = config["tip_link"].as<std::string>();

    // 加载 URDF 文件到字符串
    std::ifstream urdf_file(urdf_path);
    std::stringstream buffer;
    buffer << urdf_file.rdbuf();
    std::string urdf_str = buffer.str();

    // 使用 kdl_parser 从字符串构建 KDL::Tree
    KDL::Tree my_tree;
    if (!kdl_parser::treeFromString(urdf_str, my_tree)) {
        ROS_ERROR("Failed to parse URDF string");
        return -1;
    }

    // 获取链
    KDL::Chain chain;
    if (!my_tree.getChain(base_link, tip_link, chain)) {
        ROS_ERROR("Failed to extract KDL chain from tree");
        return -1;
    }

    // 读取关节上下限
    size_t nj = chain.getNrOfJoints();
    KDL::JntArray q_min(nj), q_max(nj);
    size_t idx = 0;

    for (const auto& segment : chain.segments) {
        const auto& joint = segment.getJoint();
        if (joint.getType() != KDL::Joint::None) {
            std::string name = joint.getName();
            auto joint_limit = config["joint_limits"][name];
            if (joint_limit) {
                q_min(idx) = joint_limit[0].as<double>();
                q_max(idx) = joint_limit[1].as<double>();
            } else {
                q_min(idx) = -M_PI;
                q_max(idx) =  M_PI;
            }
            ++idx;
        }
    }

    // 初始化 TRAC-IK
    double timeout = 0.005, eps = 1e-5;
    TRAC_IK::TRAC_IK ik_solver(chain, q_min, q_max, timeout, eps, TRAC_IK::Speed);

    // 准备一个目标位姿
    KDL::Vector pos(0.4, 0.2, 0.5);
    KDL::Rotation rot = KDL::Rotation::RPY(0, 1.57, 0);
    KDL::Frame goal_pose(rot, pos);

    // 初始种子
    KDL::JntArray q_init(nj);
    for (unsigned i = 0; i < nj; ++i)
        q_init(i) = 0.0;

    KDL::JntArray result;
    bool solved = ik_solver.CartToJnt(q_init, goal_pose, result) >= 0;

    if (solved) {
        ROS_INFO("IK Solved:");
        for (size_t i = 0; i < result.rows(); ++i) {
            ROS_INFO("Joint %lu: %.4f", i, result(i));
        }
    } else {
        ROS_WARN("IK Failed");
    }

    return 0;
}
