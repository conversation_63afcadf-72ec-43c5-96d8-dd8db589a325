#!/usr/bin/env python3
import mujoco
import mujoco.viewer
import numpy as np
import time
import os

def main():
    # 获取当前脚本的目录
    current_dir = os.path.dirname(os.path.abspath(__file__))
    
    # 构建模型文件的绝对路径
    model_path = os.path.join(current_dir, "../description/urdf/arms_gen2_hand_mujoco.xml")
    
    print(f"加载模型: {model_path}")
    
    # 加载模型
    model = mujoco.MjModel.from_xml_path(model_path)
    data = mujoco.MjData(model)
    
    # 设置初始状态
    mujoco.mj_resetData(model, data)
    
    # 使用launch而不是launch_passive以获得更完整的交互体验
    # 这将阻塞直到查看器关闭
    mujoco.viewer.launch(model, data)

if __name__ == "__main__":
    main()
