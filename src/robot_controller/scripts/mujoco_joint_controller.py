#!/usr/bin/env python3
import mujoco
import mujoco.viewer
import numpy as np
import time
import os
import rospy
from sensor_msgs.msg import JointState
import threading

class MujocoJointController:
    def __init__(self, model_path):
        # 初始化ROS节点
        rospy.init_node('mujoco_joint_controller', anonymous=True)
        
        # 加载MuJoCo模型
        self.model = mujoco.MjModel.from_xml_path(model_path)
        self.data = mujoco.MjData(self.model)
        
        # 初始化模型数据
        mujoco.mj_resetData(self.model, self.data)
        
        # 创建锁以保护数据访问
        self.lock = threading.Lock()
        
        # 获取关节名称和索引映射
        self.joint_map = {}
        for i in range(self.model.njnt):
            name = mujoco.mj_id2name(self.model, mujoco.mjtObj.mjOBJ_JOINT, i)
            if name:
                self.joint_map[name] = i
        
        print(f"模型中的关节: {list(self.joint_map.keys())}")
        
        # 订阅关节状态话题
        rospy.Subscriber('/joint_states', JointState, self.joint_states_callback, queue_size=1)
        
        # 发布当前关节状态
        self.joint_pub = rospy.Publisher('/mujoco/joint_states', JointState, queue_size=1)
        
        # 模拟运行标志
        self.running = True
        
        print("MuJoCo关节控制器初始化完成")
    
    def joint_states_callback(self, msg):
        """处理关节状态消息的回调函数"""
        with self.lock:
            # 遍历消息中的关节
            for i, name in enumerate(msg.name):
                if name in self.joint_map:
                    # 获取关节在模型中的索引
                    joint_id = self.joint_map[name]
                    
                    # 获取关节对应的qpos索引
                    qpos_addr = self.model.jnt_qposadr[joint_id]
                    
                    # 设置关节位置
                    self.data.qpos[qpos_addr] = msg.position[i]
            
            # 前向动力学更新，确保模型状态一致
            mujoco.mj_forward(self.model, self.data)
            
            rospy.logdebug(f"已更新关节位置")
    
    def publish_joint_states(self):
        """发布当前关节状态"""
        msg = JointState()
        msg.header.stamp = rospy.Time.now()
        
        # 添加所有关节的信息
        for name, idx in self.joint_map.items():
            qpos_addr = self.model.jnt_qposadr[idx]
            qvel_addr = self.model.jnt_dofadr[idx]
            
            msg.name.append(name)
            msg.position.append(self.data.qpos[qpos_addr])
            msg.velocity.append(self.data.qvel[qvel_addr])
        
        self.joint_pub.publish(msg)
    
    def run_simulation(self):
        """运行模拟"""
        # 启动MuJoCo查看器
        with mujoco.viewer.launch_passive(self.model, self.data) as viewer:
            # 设置相机视角
            viewer.cam.distance = 2.0
            viewer.cam.azimuth = 180.0
            viewer.cam.elevation = -15.0
            viewer.cam.lookat[0] = 0.0
            viewer.cam.lookat[1] = 0.0
            viewer.cam.lookat[2] = 0.5
            
            print("模拟开始，按Esc键退出...")
            
            # 运行模拟循环
            while viewer.is_running() and self.running and not rospy.is_shutdown():
                with self.lock:
                    # 步进模拟
                    mujoco.mj_step(self.model, self.data)
                
                # 发布关节状态
                self.publish_joint_states()
                
                # 更新查看器
                viewer.sync()
                
                # 控制模拟速度
                time.sleep(0.01)
    
    def stop(self):
        """停止模拟"""
        self.running = False

def main():
    # 获取当前脚本的目录
    current_dir = os.path.dirname(os.path.abspath(__file__))
    
    # 构建模型文件的绝对路径
    model_path = os.path.join(current_dir, "../description/urdf/arms_gen2_hand_mujoco.xml")
    
    print(f"加载模型: {model_path}")
    
    # 创建控制器
    controller = MujocoJointController(model_path)
    
    try:
        # 运行模拟
        controller.run_simulation()
    except KeyboardInterrupt:
        print("程序被用户中断")
    except Exception as e:
        print(f"发生错误: {e}")
    finally:
        controller.stop()
        print("模拟结束")

if __name__ == "__main__":
    main()