#!/usr/bin/env python3

import rospy
import tf
from geometry_msgs.msg import PoseStamped
from visualization_msgs.msg import Mark<PERSON>, <PERSON>er<PERSON>rray
from std_msgs.msg import ColorRGBA

class PoseRvizVisualizer:
    def __init__(self):
        # Initialize ROS node
        rospy.init_node('pose_rviz_visualizer', anonymous=True)

        # Initialize pose data
        self.left_pose = None
        self.right_pose = None

        # Subscribe to pose topics
        rospy.Subscriber('/left_hand_pose', PoseStamped, self.left_pose_callback)
        rospy.Subscriber('/right_hand_pose', PoseStamped, self.right_pose_callback)

        # Create publishers for visualization markers (only for text labels)
        self.marker_pub = rospy.Publisher('/hand_pose_markers', MarkerArray, queue_size=10)
        self.tf_broadcaster = tf.TransformBroadcaster()

        # Set update rate
        self.rate = rospy.Rate(10)  # 10Hz

        # Initialize marker IDs
        self.text_id_start = 0  # IDs 0, 1 for text labels
        self.sphere_id_start = 2  # IDs 2, 3 for sphere markers

        # Set colors
        self.left_color = ColorRGBA(1.0, 0.0, 0.0, 1.0)  # Red
        self.right_color = ColorRGBA(0.0, 0.0, 1.0, 1.0)  # Blue

        print("RViz visualizer initialized. Please add MarkerArray display in RViz with topic '/hand_pose_markers'")
        print("Also add TF display to see coordinate frames")

    def left_pose_callback(self, msg):
        """Left hand pose callback"""
        self.left_pose = msg

    def right_pose_callback(self, msg):
        """Right hand pose callback"""
        self.right_pose = msg





    def create_text_marker(self, pose, marker_id, hand_name, color, z_offset=0.05):
        """Create a text marker for displaying coordinates"""
        marker = Marker()
        marker.header.frame_id = "footprint"
        marker.header.stamp = rospy.Time.now()
        marker.ns = "hand_text"
        marker.id = marker_id
        marker.type = Marker.TEXT_VIEW_FACING
        marker.action = Marker.ADD

        # Set position (slightly above the hand)
        marker.pose = pose
        marker.pose.position.z += z_offset

        # Set scale (text size)
        marker.scale.z = 0.05

        # Set color
        marker.color = color

        # Set text with coordinates
        pos = pose.position
        marker.text = f"{hand_name}: ({pos.x:.2f}, {pos.y:.2f}, {pos.z:.2f})"

        return marker

    def create_sphere_marker(self, pose, marker_id, color, radius=0.01):
        """Create a sphere marker for visualizing hand position"""
        marker = Marker()
        marker.header.frame_id = "footprint"
        marker.header.stamp = rospy.Time.now()
        marker.ns = "hand_sphere"
        marker.id = marker_id
        marker.type = Marker.SPHERE
        marker.action = Marker.ADD

        # Set position
        marker.pose = pose

        # Set scale (sphere size)
        marker.scale.x = radius * 1
        marker.scale.y = radius * 1
        marker.scale.z = radius * 1

        # Set color
        marker.color = color

        return marker

    def broadcast_tf_frames(self):
        """Broadcast TF frames for the hand poses"""
        if self.left_pose:
            p = self.left_pose.pose.position
            q = self.left_pose.pose.orientation
            self.tf_broadcaster.sendTransform(
                (p.x, p.y, p.z),
                (q.x, q.y, q.z, q.w),
                rospy.Time.now(),
                "left_hand",
                "footprint"
            )

        if self.right_pose:
            p = self.right_pose.pose.position
            q = self.right_pose.pose.orientation
            self.tf_broadcaster.sendTransform(
                (p.x, p.y, p.z),
                (q.x, q.y, q.z, q.w),
                rospy.Time.now(),
                "right_hand",
                "footprint"
            )

    def update_visualization(self):
        """Update visualization markers"""
        marker_array = MarkerArray()

        # Add left hand markers if pose is available
        if self.left_pose:
            # Add text marker with coordinates
            marker_array.markers.append(
                self.create_text_marker(
                    self.left_pose.pose,
                    self.text_id_start,
                    "Left",
                    self.left_color
                )
            )

            # Add sphere marker for left hand
            marker_array.markers.append(
                self.create_sphere_marker(
                    self.left_pose.pose,
                    self.sphere_id_start,
                    self.left_color,
                    radius=0.03  # 3cm radius
                )
            )

        # Add right hand markers if pose is available
        if self.right_pose:
            # Add text marker with coordinates
            marker_array.markers.append(
                self.create_text_marker(
                    self.right_pose.pose,
                    self.text_id_start + 1,
                    "Right",
                    self.right_color
                )
            )

            # Add sphere marker for right hand
            marker_array.markers.append(
                self.create_sphere_marker(
                    self.right_pose.pose,
                    self.sphere_id_start + 1,
                    self.right_color,
                    radius=0.03  # 3cm radius
                )
            )

        # Publish marker array
        if marker_array.markers:
            self.marker_pub.publish(marker_array)

        # Broadcast TF frames
        self.broadcast_tf_frames()

    def run(self):
        """Run visualization loop"""
        try:
            while not rospy.is_shutdown():
                self.update_visualization()
                self.rate.sleep()
        except KeyboardInterrupt:
            print("Visualization interrupted by user")
        except Exception as e:
            print(f"Visualization error: {e}")

if __name__ == '__main__':
    try:
        visualizer = PoseRvizVisualizer()
        visualizer.run()
    except rospy.ROSInterruptException:
        pass
