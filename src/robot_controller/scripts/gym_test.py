from isaacgym import gymapi
import math

# 初始化 Gym
gym = gymapi.acquire_gym()

# 设置仿真参数
sim_params = gymapi.SimParams()
sim_params.dt = 1.0 / 60.0
sim_params.up_axis = gymapi.UP_AXIS_Z
sim_params.gravity = gymapi.Vec3(0.0, 0.0, -9.81)
sim_params.physx.use_gpu = True

sim = gym.create_sim(0, 0, gymapi.SIM_PHYSX, sim_params)
if sim is None:
    print("*** Failed to create sim")
    quit()

# 添加地面
plane_params = gymapi.PlaneParams()
plane_params.normal = gymapi.Vec3(0.0, 0.0, 1.0)
gym.add_ground(sim, plane_params)

# 创建一个环境
env = gym.create_env(sim,
                     gymapi.Vec3(-1.0, -1.0, 0.0),
                     gymapi.Vec3(1.0, 1.0, 2.0),
                     1)

# 加载 URDF 文件
asset_root = "../description/urdf"
urdf_path = "arms_gen2_hand_gym.urdf"  # 修改为你自己的路径
asset_options = gymapi.AssetOptions()
asset_options.fix_base_link = True  # 如果需要固定机器人底座
asset = gym.load_asset(sim, asset_root, urdf_path, asset_options)

# 创建 Actor
pose = gymapi.Transform()
pose.p = gymapi.Vec3(0.0, 0.0, 1.0)  # 把机器人放高一些防止穿地
pose.r = gymapi.Quat(0.0, 0.0, 0.0, 1.0)
actor_handle = gym.create_actor(env, asset, pose, "robot", 0, 1)

# 创建 viewer
viewer = gym.create_viewer(sim, gymapi.CameraProperties())
if viewer is None:
    print("*** Failed to create viewer")
    quit()

# 设置视角
cam_pos = gymapi.Vec3(2.0, 2.0, 2.0)
cam_target = gymapi.Vec3(0.0, 0.0, 1.0)
gym.viewer_camera_look_at(viewer, None, cam_pos, cam_target)

# 主循环
while not gym.query_viewer_has_closed(viewer):
    gym.simulate(sim)
    gym.fetch_results(sim, True)
    gym.step_graphics(sim)
    gym.render_all_camera_sensors(sim)
    gym.refresh_actor_root_state_tensor(sim)
    gym.draw_viewer(viewer, sim, True)
    gym.sync_frame_time(sim)

# 清理资源
gym.destroy_viewer(viewer)
gym.destroy_sim(sim)
