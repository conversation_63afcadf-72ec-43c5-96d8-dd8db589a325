/usr/bin/c++ -fPIC -shared -Wl,-soname,librobot_ctrl_lib.so -o /home/<USER>/arm_control_ws/devel/lib/librobot_ctrl_lib.so CMakeFiles/robot_ctrl_lib.dir/utils/AnomDetector/anomaly_detector.cpp.o CMakeFiles/robot_ctrl_lib.dir/utils/AnomDetector/coal_wrapper.cpp.o CMakeFiles/robot_ctrl_lib.dir/utils/AnomDetector/joint_fault_monitor.cpp.o CMakeFiles/robot_ctrl_lib.dir/utils/AnomDetector/self_collision_detector.cpp.o CMakeFiles/robot_ctrl_lib.dir/utils/CallBackFcn/get_pos_based_cv_topic.cpp.o CMakeFiles/robot_ctrl_lib.dir/utils/CallBackFcn/get_qpos_act.cpp.o CMakeFiles/robot_ctrl_lib.dir/utils/DataUtils/data_print.cpp.o CMakeFiles/robot_ctrl_lib.dir/utils/DataUtils/data_save.cpp.o CMakeFiles/robot_ctrl_lib.dir/utils/KinematicsSolver/kdl_solver.cpp.o CMakeFiles/robot_ctrl_lib.dir/utils/KinematicsSolver/relaxed_ik_solver.cpp.o CMakeFiles/robot_ctrl_lib.dir/utils/KinematicsSolver/spatial_transform.cpp.o CMakeFiles/robot_ctrl_lib.dir/utils/RobotController/controller_init.cpp.o CMakeFiles/robot_ctrl_lib.dir/utils/RobotController/arm_control.cpp.o CMakeFiles/robot_ctrl_lib.dir/utils/RobotController/inspire_hand_control.cpp.o CMakeFiles/robot_ctrl_lib.dir/utils/TrajPlanner/TrajPlan_Adjust.cpp.o CMakeFiles/robot_ctrl_lib.dir/utils/TrajPlanner/TrajPlan_DMP.cpp.o CMakeFiles/robot_ctrl_lib.dir/utils/TrajPlanner/TrajPlan_Inter.cpp.o   -L/opt/ros/noetic/lib  -Wl,-rpath,/home/<USER>/miniconda3/envs/coal/lib:/opt/ros/noetic/lib 
